<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useTTSStore } from '../stores/ttsStore';

const ttsStore = useTTSStore();
const cookie = ref(ttsStore.cookie);
const workspaceId = ref(ttsStore.workspaceId);
const tiktokSessionId = ref(ttsStore.tiktokSessionId);
const showCookie = ref(false);
const speechRate = ref(0);
const pitchRate = ref(0);

// Transform speech rate value to a multiplier format (1x, 1.2x, etc.)
function formatSpeechRate(value) {
  // Convert from slider value (-50 to 100) to multiplier (0.5x to 2x)
  const multiplier = 1 + (value / 100);
  return `${multiplier.toFixed(1)}x`;
}

function parseSpeechRate(value) {
  // Convert from multiplier format (e.g., "1.2x") back to slider value
  const multiplier = parseFloat(value.replace('x', ''));
  return Math.round((multiplier - 1) * 100);
}

function formatPitchRate(value) {
  return `${value}`;
}

function parsePitchRate(value) {
  return parseInt(value);
}

function saveCookie() {
  ttsStore.setCookie(cookie.value);
  ttsStore.setWorkspaceId(workspaceId.value);
  ttsStore.setTypeEngine(ttsStore.typeEngine);
  ttsStore.setTiktokSessionId(tiktokSessionId.value);
  // ttsStore.setOpenaiKey(ttsStore.openaiKey);
  // ttsStore.setDeepseekKey(ttsStore.deepseekKey);
  // ttsStore.setGeminiKey(ttsStore.geminiKey);
  // ttsStore.setModel(ttsStore.model);

  electronAPI.setSession({
    cookie: cookie.value,
    workspaceId: workspaceId.value,
    tiktokSessionId: tiktokSessionId.value,
  });
  message.success('Configuration saved!');
}

function callChangeTypeEngine() {
  setTimeout(() => {
    ttsStore.fetchSpeakers()
  }, 500);
}

function updateSpeechRate(value) {
  speechRate.value = value;
  ttsStore.updateTTSParams({ speech_rate: value });
}

function updatePitchRate(value) {
  pitchRate.value = value;
  ttsStore.updateTTSParams({ pitch_rate: value });
}

function openFolder() {
  electronAPI.openFolder()
}

onMounted(() => {
  cookie.value = ttsStore.cookie;
  workspaceId.value = ttsStore.workspaceId;
  speechRate.value = ttsStore.params?.speech_rate || 0;
  pitchRate.value = ttsStore.params?.pitch_rate || 0;
});
</script>

<template>
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Configuration</h2>
    
    <div class="space-y-4">
      <div>
        <label for="typeEngine" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Type Engine
        </label>
        <a-radio-group v-model:value="ttsStore.typeEngine" @change="callChangeTypeEngine" class="mt-1">
          <a-radio value="capcut">CapCut</a-radio>
          <a-radio value="tiktok">Tiktok</a-radio>
        </a-radio-group>
      </div>
      
      <div v-if="ttsStore.typeEngine === 'capcut'">
        <label for="workspaceId" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Workspace ID
        </label>
        <a-input
          id="workspaceId"
          v-model:value="workspaceId"
          placeholder="Enter your CapCut workspace ID"
          class="mt-1"
        />
      </div>
      <div v-if="ttsStore.typeEngine === 'tiktok'">
        <label for="tiktokSessionId" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Tiktok Session ID
        </label>
        <a-input
          id="tiktokSessionId"
          v-model:value="tiktokSessionId"
          placeholder="Enter your Tiktok Session ID"
          class="mt-1"
        />
      </div>
      <div v-if="ttsStore.typeEngine === 'capcut'">
        <label for="cookie" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Cookie
        </label>
        <div class="mt-1 relative">
          <a-input
            id="cookie"
            v-model:value="cookie"
            :type="showCookie ? 'text' : 'password'"
            placeholder="Enter your CapCut cookie"
            :rows="3"
          />
          <button
            type="button"
            @click="showCookie = !showCookie"
            class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            <span v-if="showCookie">Hide</span>
            <span v-else>Show</span>
          </button>
        </div>
      </div>

      <!-- Speech Rate Slider -->
      <div v-if="ttsStore.typeEngine === 'capcut'">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Speed (Tốc độ)
        </label>
        <div class="flex items-center">
          <a-slider
            v-model:value="speechRate"
            :min="-50"
            :max="100"
            :step="1"
            :marks="{
              '-50': '',
              '0': '',
              '50': '',
              '100': ''
            }"
            @change="updateSpeechRate"
            class="flex-1 mr-3"
          />
          <a-input
            class="w-16"
            size="small"
            :value="formatSpeechRate(speechRate)"
            disabled
          />
        </div>
      </div>

      <!-- Pitch Rate Slider -->
      <div v-if="ttsStore.typeEngine === 'capcut'">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Pitch (Độ cao)
        </label>
        <div class="flex items-center">
          <a-slider
            v-model:value="pitchRate"
            :min="-12"
            :max="12"
            :step="1"
            :marks="{
              '-12': '',
              '0': '',
              '12': ''
            }"
            @change="updatePitchRate"
            class="flex-1 mr-3"
          />
          <a-input
            class="w-16"
            size="small"
            :value="formatPitchRate(pitchRate)"
            disabled
          />
        </div>
      </div>

      <!-- api openai, deepseek, gemini key -->
      <div>
        <label for="openaiKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          OpenAI Key
        </label>
        <a-input
          id="openaiKey"
          v-model:value="ttsStore.openaiKey"
          placeholder="Enter your OpenAI Key"
          class="mt-1"
        />
      </div>
      <div>
        <label for="deepseekKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          DeepSeek Key
        </label>
        <a-input
          id="deepseekKey"
          v-model:value="ttsStore.deepseekKey"
          placeholder="Enter your DeepSeek Key"
          class="mt-1"
        />
      </div>
      <div>
        <label for="geminiKey" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Gemini Key
        </label>
        <a-input
          id="geminiKey"
          v-model:value="ttsStore.geminiKey"
          placeholder="Enter your Gemini Key"
          class="mt-1"
        />
      </div>
      <div>
        <label for="model" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Model
        </label>
        <a-select
          id="model"
          v-model:value="ttsStore.model"
          class="w-full mt-1"
          placeholder="Select a model"
        >
          <a-select-option value="deepseek-chat">DeepSeek</a-select-option>
          <a-select-option value="gpt-3.5-turbo">GPT-3.5 Turbo</a-select-option>
          <a-select-option value="gpt-4o">GPT-4o</a-select-option>
        </a-select>
      </div>

      
      <div class="flex justify-end gap-4">
        <a-button type="primary" @click="openFolder">
          Open folder audio
        </a-button>
        <a-button type="primary" @click="saveCookie">
          Save Configuration
        </a-button>
      </div>
    </div>
  </div>
</template>