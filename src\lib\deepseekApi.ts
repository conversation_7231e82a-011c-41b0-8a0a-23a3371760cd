"use client";

import { RateLimiter } from "./rateLimiter";

// Create a global rate limiter instance with 2s delay and 3 retries
const rateLimiter = new RateLimiter(2000, 3, 2);

// Variable to store the current API key
let currentApiKey = "";

/**
 * Update the API key
 * @param apiKey New API key
 */
export function setApiKey(apiKey: string) {
  currentApiKey = apiKey;
}

/**
 * Get the current API key
 * @returns Current API key
 */
export function getApiKey(): string {
  return currentApiKey;
}

export interface TranslationResult {
  text: string;
  error?: string;
}

export interface TranslateOptions {
  texts: string[];
  targetLanguage: string;
  prompt: string;
  context?: string;
  model?: string;
}

/**
 * Translate an array of texts using the DeepSeek API
 */
export async function translateWithDeepseek({
  texts,
  targetLanguage,
  prompt,
  context = "",
}: TranslateOptions): Promise<TranslationResult[]> {
  try {
    // Check if API key is provided
    if (!currentApiKey) {
      throw new Error("API key is required. Please provide a valid DeepSeek API key.");
    }
    
    // Use the rate limiter to execute the API call
    return await rateLimiter.execute(async () => {
      // DeepSeek API endpoint
      const apiUrl = "https://api.deepseek.com/v1/chat/completions";
      
      // Prepare the prompt template for batch translation
      const promptTemplate = `${prompt}

${context ? context + "\n\n" : ""}Respond in a JSON format with an array of translated texts.

For example:
Input: ["Hello, how are you?", "I'm fine, thank you."]
Output: { "translations": ["Xin chào, bạn khỏe không?", "Tôi khỏe, cảm ơn bạn."] }

Here are the texts to translate:
${JSON.stringify(texts)}`;

      // Call the DeepSeek API
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${currentApiKey}`
        },
        body: JSON.stringify({
          model: "deepseek-chat", // Default model
          messages: [
            {
              role: "user",
              content: promptTemplate
            }
          ],
          temperature: 0.3,
          max_tokens: 8192
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`DeepSeek API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data = await response.json();
      const responseText = data.choices?.[0]?.message?.content || "";
      
      // Parse the JSON response
      try {
        // Extract JSON from the response (handling potential text before/after the JSON)
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        
        if (!jsonMatch) {
          // If no JSON found, try to parse lines directly
          const lines = responseText.split("\n").filter(line => line.trim());
          const translations = lines.map(line => {
            // Remove any numbering, quotes, etc.
            return line.replace(/^\d+[\.\)]?\s*["']?|["']?\s*$/, "").trim();
          });
          
          // Make sure we have the right number of translations
          return texts.map((_, index) => ({
            text: translations[index] || `[Error: Failed to extract translation ${index + 1}]`
          }));
        }
        
        const jsonStr = jsonMatch[0];
        const parsed = JSON.parse(jsonStr);
        
        // Check if the response has the expected format
        if (Array.isArray(parsed.translations)) {
          return parsed.translations.map((text: string) => ({ text }));
        } else if (parsed.translations) {
          // Handle unexpected format but still has translations field
          return texts.map((_, index) => ({
            text: String(parsed.translations[index] || "")
          }));
        } else {
          // Fall back to using any array in the response
          const firstArrayField = Object.values(parsed).find(value => Array.isArray(value));
          
          if (firstArrayField) {
            return (firstArrayField as string[]).map(text => ({ text }));
          } else {
            throw new Error("Response doesn't contain translations in expected format");
          }
        }
      } catch (parseError) {
        console.error("Error parsing API response:", parseError);
        
        // Split by new lines as fallback
        const lines = responseText
          .split("\n")
          .filter(line => line.trim())
          .map(line => line.replace(/^\d+[\.\)]?\s*["']?|["']?\s*$/, "").trim());
        
        return texts.map((_, index) => ({
          text: lines[index] || `[Error: Failed to parse response for item ${index + 1}]`
        }));
      }
    });
  } catch (error) {
    console.error("DeepSeek API error:", error);
    return texts.map(() => ({
      text: "",
      error: error instanceof Error ? error.message : "Unknown error occurred"
    }));
  }
}
