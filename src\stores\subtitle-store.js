
import { defineStore } from 'pinia';
import { message } from 'ant-design-vue';
// import { parseSRT } from '../lib/utils';

export const useSubtitleStore = defineStore('subtitle', {
  state: () => ({
    subtitles: [],
    isLoading: false,
    error: null,
    currentPage: 1,
    pageSize: 10,
    totalPages: 0,
    editingId: null,
    editText: '',
    retryingBatch: null,
    expandedTable: false,
  }),
  actions: {
    setSubtitles(subtitles) {
      this.subtitles = subtitles;
      this.totalPages = Math.ceil(subtitles.length / this.pageSize);
    },
    setIsLoading(value) {
      this.isLoading = value;
    },
    setError(error) {
      this.error = error;
    },
    setCurrentPage(page) {
      this.currentPage = page;
    },
    setEditingId(id) {
      this.editingId = id;
    },
    setEditText(text) {
      this.editText = text;
    },
    setRetryingBatch(batch) {
      this.retryingBatch = batch;
    },
    setExpandedTable(value) {
      this.expandedTable = value;
    },
  },
});

