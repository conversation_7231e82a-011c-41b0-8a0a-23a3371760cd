import cv2
import difflib
import os
import re

from utils import init_args
from .text_ocr import TextOcr
from typing import List, Optional, Dict

class VideoSubtitleExtractor:
    def __init__(self, lang: str = "zh", use_gpu: bool = False, video_path: str = None, output_path: str = None) -> None:
        """
        Initialize the video subtitle extractor with memory-efficient processing.

        :param lang: Language code for subtitle extraction
        """
        self.args = init_args(lang, use_gpu, video_path, output_path)
        print("self", self.args)
        self.args.warmup = True

        self.text_sys = TextOcr(self.args)
        self.previous_subtitles = []
        self.line_separator = " | "  # Separator for multiple lines in output

    def extract_subtitles(self, video_path: str, frame_rate: int = 1,
                          confidence_threshold: float = 0.5,
                          subtitle_disappear_threshold: int = 10,
                          progress_bar=None) -> List[Dict]:
        """
        Extract subtitles from video with precise timing and memory efficiency.

        :param video_path: Path to the video file
        :param frame_rate: Number of frames to skip between processed frames
        :param confidence_threshold: Minimum confidence threshold for subtitle recognition
        :param subtitle_disappear_threshold: Number of consecutive frames without subtitles before considering it disappeared
        :param progress_bar: Streamlit progress bar object (optional)
        :return: List of extracted subtitles with accurate timing
        """
        cap = cv2.VideoCapture(video_path)

        # Get video metadata
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # Calculate frame skip interval
        frame_skip = max(1, int(fps // frame_rate))

        # Subtitle tracking variables
        subtitles = []
        current_subtitle = None
        frames_without_subtitle = 0
        frame_count = 0
        last_subtitle_frame = -1
        last_valid_subtitle_frame = -1

        try:
            while True:
                success, frame = cap.read()

                if not success:
                    break

                if frame_count % frame_skip == 0:
                    _, rec_res = self.text_sys(frame)

                    if rec_res:
                        frame_subtitles = []
                        current_group = []
                        current_confidence = 0.0

                        for text, conf in rec_res:
                            if conf >= confidence_threshold and text.strip():
                                cleaned_text = self._remove_timestamp(text.strip())
                                if cleaned_text:
                                    current_group.append(cleaned_text)
                                    current_confidence = max(current_confidence, conf)

                        if current_group:
                            combined_text = "\n".join(current_group)
                            frame_subtitles.append((combined_text, current_confidence))

                        if frame_subtitles:
                            best_subtitle = max(frame_subtitles, key=lambda x: (x[1], len(x[0])))[0]

                            # Check for uniqueness
                            is_unique = all(
                                self._compute_similarity(best_subtitle, prev) < 0.8
                                for prev in self.previous_subtitles[-10:]
                            )

                            if is_unique:
                                if current_subtitle:
                                    current_subtitle['end_time'] = self._format_timestamp(
                                        last_valid_subtitle_frame / fps
                                    )
                                    subtitles.append(current_subtitle)

                                current_subtitle = {
                                    'start_time': self._format_timestamp(frame_count / fps),
                                    'end_time': None,
                                    'text': best_subtitle
                                }

                                frames_without_subtitle = 0
                                last_valid_subtitle_frame = frame_count
                                self.previous_subtitles.append(best_subtitle)

                            last_valid_subtitle_frame = frame_count
                            frames_without_subtitle = 0

                    else:
                        frames_without_subtitle += 1

                    if current_subtitle and frames_without_subtitle >= subtitle_disappear_threshold:
                        current_subtitle['end_time'] = self._format_timestamp(
                            last_valid_subtitle_frame / fps
                        )
                        subtitles.append(current_subtitle)
                        current_subtitle = None

                if progress_bar and frame_count % max(1, total_frames // 100) == 0:
                    progress_bar.progress(int(frame_count / total_frames * 100))

                frame_count += 1

                if frame_count >= total_frames:
                    break

            if current_subtitle:
                current_subtitle['end_time'] = self._format_timestamp(
                    last_valid_subtitle_frame / fps
                )
                subtitles.append(current_subtitle)

        finally:
            cap.release()

        return self._remove_duplicate_subtitles(subtitles)

    def _compute_similarity(self, str1: str, str2: str) -> float:
        """
        Compute similarity between two strings, handling multi-line text
        """
        clean_str1 = self._normalize_text(str1)
        clean_str2 = self._normalize_text(str2)

        clean_str1 = clean_str1.replace(' ', '').lower()
        clean_str2 = clean_str2.replace(' ', '').lower()

        return difflib.SequenceMatcher(None, clean_str1, clean_str2).ratio()

    def _normalize_text(self, text: str) -> str:
        """
        Optimized text normalization for multi-line subtitles

        :param text: Text to normalize
        :return: Text after normalization
        """
        lines = re.split(r'[\n\r]+', text)

        cleaned_lines = []
        for line in lines:
            line = ' '.join(line.split())
            if line:
                cleaned_lines.append(line)

        return "\n".join(cleaned_lines)

    def _format_timestamp(self, seconds: float) -> str:
        """
        Convert seconds to SRT timestamp format
        """
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        millisecs = int((secs - int(secs)) * 1000)

        return f"{hours:02d}:{minutes:02d}:{int(secs):02d},{millisecs:03d}"

    def _remove_timestamp(self, text: str) -> str:
        """
        Remove timestamps from subtitle text

        :param text: Raw subtitle text
        :return: Cleaned subtitle text
        """
        timestamp_pattern = r'\d{1,2}:\d{2}(:\d{2})?(,\d{3})?|\d{1,2}:\d{2}(:\d{2})?(\.\d{3})?'
        return re.sub(timestamp_pattern, '', text).strip()

    def _remove_duplicate_subtitles(self, subtitles: List[Dict]) -> List[Dict]:
        """
        Remove duplicate subtitles based on text content

        :param subtitles: List of subtitle dictionaries
        :return: List of unique subtitles
        """
        seen_texts = set()
        unique_subtitles = []

        for subtitle in subtitles:
            text = subtitle['text']
            normalized_text = self._normalize_text(text).replace(' ', '').lower()
            if normalized_text not in seen_texts:
                seen_texts.add(normalized_text)
                unique_subtitles.append(subtitle)

        return unique_subtitles

    def get_video_metadata(self, video_path: str) -> Optional[dict]:
        """
        Efficiently extract video metadata

        :param video_path: Path to the input video file
        :return: Dictionary of video metadata or None
        """
        try:
            cap = cv2.VideoCapture(video_path)

            metadata = {
                'fps': cap.get(cv2.CAP_PROP_FPS),
                'total_frames': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
                'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                'duration': cap.get(cv2.CAP_PROP_FRAME_COUNT) / cap.get(cv2.CAP_PROP_FPS)
            }

            return metadata
        except Exception as e:
            return None
        finally:
            cap.release()
