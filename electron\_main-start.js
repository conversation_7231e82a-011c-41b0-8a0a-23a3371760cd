// const AdvancedFFmpegUtils = require("./utils/ffmpeg-utils");
// const callProcessor = require("./utils/FFmpegVideoProcessor");
// const { VideoSimplified } = require("./utils/VideoSimplified");
const { processVideoSimplified } = require("./utils/videoRendererSimplified");

// const srtArray =[
//     {
//         "index": 1,
//         "id": 1,
//         "text": "你知道人贩子为什么喜欢偷小孩吗?",
//         "startTime": 0.24,
//         "endTime": 2.7199999999999998,
//         "start": "00:00:00,240",
//         "end": "00:00:02,720",
//         "translatedText": "Con có biết tại sao bọn buôn người thích bắt cóc trẻ em không?",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\con-co-biet-tai-sao-bon-1747960196878.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\con-co-biet-tai-sao-bon-1747960196878.mp3",
//         "audioDuration1": 2745,
//         "isGenerated1": true
//     },
//     {
//         "index": 2,
//         "id": 2,
//         "text": "不知道",
//         "startTime": 3.12,
//         "endTime": 3.48,
//         "start": "00:00:03,120",
//         "end": "00:00:03,480",
//         "translatedText": "Con không biết",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\con-khong-biet1747960198880.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 2,
//         "audioUrl2": "file://F:\\Footage\\0. any\\_-converted_audio\\con-khong-biet1747960198880.mp3",
//         "audioDuration2": 745,
//         "isGenerated2": true
//     },
//     {
//         "index": 3,
//         "id": 3,
//         "text": "因为你的脑神经值265万",
//         "startTime": 3.48,
//         "endTime": 6.74,
//         "start": "00:00:03,480",
//         "end": "00:00:06,740",
//         "translatedText": "Vì dây thần kinh não của con trị giá 2 phẩy 65 triệu tệ, khoảng 9 phẩy 5 tỷ tiền việt",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-day-than-kinh-nao-cua1747960199621.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-day-than-kinh-nao-cua1747960199621.mp3",
//         "audioDuration1": 5120,
//         "isGenerated1": true
//     },
//     {
//         "index": 4,
//         "id": 4,
//         "text": "小心脏值75万",
//         "startTime": 6.74,
//         "endTime": 9.32,
//         "start": "00:00:06,740",
//         "end": "00:00:09,320",
//         "translatedText": "Tim nhỏ trị giá 750 nghìn",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\tim-nho-tri-gia-750-nghi1747960201734.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\tim-nho-tri-gia-750-nghi1747960201734.mp3",
//         "audioDuration1": 2304,
//         "isGenerated1": true
//     },
//     {
//         "index": 5,
//         "id": 5,
//         "text": "肾脏值65万",
//         "startTime": 9.32,
//         "endTime": 11.26,
//         "start": "00:00:09,320",
//         "end": "00:00:11,260",
//         "translatedText": "Thận trị giá 650 nghìn",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\than-tri-gia-650-nghin1747960202846.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\than-tri-gia-650-nghin1747960202846.mp3",
//         "audioDuration1": 2075,
//         "isGenerated1": true
//     },
//     {
//         "index": 6,
//         "id": 6,
//         "text": "皮肤血液值120万",
//         "startTime": 11.26,
//         "endTime": 13.52,
//         "start": "00:00:11,260",
//         "end": "00:00:13,520",
//         "translatedText": "Máu da trị giá 1.2 triệu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\mau-da-tri-gia-12-trieu1747960722180.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\mau-da-tri-gia-12-trieu1747960722180.mp3",
//         "audioDuration1": 1959.184,
//         "isGenerated1": true
//     },
//     {
//         "index": 7,
//         "id": 7,
//         "text": "牙齿值20万",
//         "startTime": 13.52,
//         "endTime": 15.36,
//         "start": "00:00:13,520",
//         "end": "00:00:15,360",
//         "translatedText": "Răng trị giá 200 nghìn",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\rang-tri-gia-200-nghin1747960761500.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\rang-tri-gia-200-nghin1747960761500.mp3",
//         "audioDuration1": 1800,
//         "isGenerated1": true
//     },
//     {
//         "index": 8,
//         "id": 8,
//         "text": "眼睛值7万",
//         "startTime": 15.36,
//         "endTime": 17.14,
//         "start": "00:00:15,360",
//         "end": "00:00:17,140",
//         "translatedText": "Mắt trị giá 70 nghìn",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\mat-tri-gia-70-nghin1747960204809.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\mat-tri-gia-70-nghin1747960204809.mp3",
//         "audioDuration1": 1728,
//         "isGenerated1": true
//     },
//     {
//         "index": 9,
//         "id": 9,
//         "text": "鼻腔值10万",
//         "startTime": 17.14,
//         "endTime": 18.76,
//         "start": "00:00:17,140",
//         "end": "00:00:18,760",
//         "translatedText": "Khoang mũi trị giá 100 nghìn",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\khoang-mui-tri-gia-100-n1747960772043.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\khoang-mui-tri-gia-100-n1747960772043.mp3",
//         "audioDuration1": 1935,
//         "isGenerated1": true
//     },
//     {
//         "index": 10,
//         "id": 10,
//         "text": "肺值30万",
//         "startTime": 18.76,
//         "endTime": 20.02,
//         "start": "00:00:18,760",
//         "end": "00:00:20,020",
//         "translatedText": "Phổi trị giá 300 nghìn",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\phoi-tri-gia-300-nghin1747960205883.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\phoi-tri-gia-300-nghin1747960205883.mp3",
//         "audioDuration1": 1705,
//         "isGenerated1": true
//     },
//     {
//         "index": 11,
//         "id": 11,
//         "text": "还有我们的肝脏值90万呢",
//         "startTime": 20.02,
//         "endTime": 22.4,
//         "start": "00:00:20,020",
//         "end": "00:00:22,400",
//         "translatedText": "Gan của chúng ta cũng có giá tới 900.000 đấy",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\gan-cua-chung-ta-cung-co1747960775756.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\gan-cua-chung-ta-cung-co1747960775756.mp3",
//         "audioDuration1": 3343.673,
//         "isGenerated1": true
//     },
//     {
//         "index": 12,
//         "id": 12,
//         "text": "你要知道这些都是黑市上人体器官的一个价格",
//         "startTime": 22.4,
//         "endTime": 26.44,
//         "start": "00:00:22,400",
//         "end": "00:00:26,440",
//         "translatedText": "Bạn phải biết đây đều là giá nội tạng người trên chợ đen",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-biet-day-deu-la1747960207205.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-biet-day-deu-la1747960207205.mp3",
//         "audioDuration1": 2665,
//         "isGenerated1": true
//     },
//     {
//         "index": 13,
//         "id": 13,
//         "text": "这些要是被人贩子摘掉",
//         "startTime": 26.44,
//         "endTime": 28.74,
//         "start": "00:00:26,440",
//         "end": "00:00:28,740",
//         "translatedText": "Nếu mấy thứ này bị bọn buôn người lấy đi",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\neu-may-thu-nay-bi-bon-b1747960225248.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\neu-may-thu-nay-bi-bon-b1747960225248.mp3",
//         "audioDuration1": 2184,
//         "isGenerated1": true
//     },
//     {
//         "index": 14,
//         "id": 14,
//         "text": "会被卖到全球各地的",
//         "startTime": 28.74,
//         "endTime": 30.32,
//         "start": "00:00:28,740",
//         "end": "00:00:30,320",
//         "translatedText": "Chúng sẽ bị bán đi khắp nơi trên thế giới",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\chung-se-bi-ban-di-khap-1747960779845.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\chung-se-bi-ban-di-khap-1747960779845.mp3",
//         "audioDuration1": 2280,
//         "isGenerated1": true
//     },
//     {
//         "index": 15,
//         "id": 15,
//         "text": "所以你一定要注意",
//         "startTime": 30.32,
//         "endTime": 32.18,
//         "start": "00:00:30,320",
//         "end": "00:00:32,180",
//         "translatedText": "Vì vậy bạn nhất định phải cẩn thận",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-vay-ban-nhat-dinh-pha1747960209044.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-vay-ban-nhat-dinh-pha1747960209044.mp3",
//         "audioDuration1": 1665,
//         "isGenerated1": true
//     },
//     {
//         "index": 16,
//         "id": 16,
//         "text": "不管去哪里",
//         "startTime": 32.18,
//         "endTime": 33.08,
//         "start": "00:00:32,180",
//         "end": "00:00:33,080",
//         "translatedText": "Dù đi bất cứ đâu",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\du-di-bat-cu-dau1747960209804.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\du-di-bat-cu-dau1747960209804.mp3",
//         "audioDuration1": 1215,
//         "isGenerated1": true
//     },
//     {
//         "index": 17,
//         "id": 17,
//         "text": "一定不要单独外出",
//         "startTime": 33.08,
//         "endTime": 34.56,
//         "start": "00:00:33,080",
//         "end": "00:00:34,560",
//         "translatedText": "Tuyệt đối không đi một mình",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\tuyet-doi-khong-di-mot-m1747960782836.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\tuyet-doi-khong-di-mot-m1747960782836.mp3",
//         "audioDuration1": 1425,
//         "isGenerated1": true
//     },
//     {
//         "index": 18,
//         "id": 18,
//         "text": "不能走夜路",
//         "startTime": 34.56,
//         "endTime": 36.04,
//         "start": "00:00:34,560",
//         "end": "00:00:36,040",
//         "translatedText": "Không được đi đường đêm",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\khong-duoc-di-duong-dem1747960210672.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\khong-duoc-di-duong-dem1747960210672.mp3",
//         "audioDuration1": 1344,
//         "isGenerated1": true
//     },
//     {
//         "index": 19,
//         "id": 19,
//         "text": "一定要走有摄像头的大马路",
//         "startTime": 36.04,
//         "endTime": 38.12,
//         "start": "00:00:36,040",
//         "end": "00:00:38,120",
//         "translatedText": "Luôn đi trên những con đường lớn có camera giám sát",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\luon-di-tren-nhung-con-d1747960785148.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\luon-di-tren-nhung-con-d1747960785148.mp3",
//         "audioDuration1": 2755,
//         "isGenerated1": true
//     },
//     {
//         "index": 20,
//         "id": 20,
//         "text": "最好呢",
//         "startTime": 38.12,
//         "endTime": 39.16,
//         "start": "00:00:38,120",
//         "end": "00:00:39,160",
//         "translatedText": "Tốt nhất là...",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\tot-nhat-la1747962181580.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\tot-nhat-la1747962181580.mp3",
//         "audioDuration1": 865,
//         "isGenerated1": true
//     },
//     {
//         "index": 21,
//         "id": 21,
//         "text": "要结伴同行",
//         "startTime": 39.16,
//         "endTime": 40.16,
//         "start": "00:00:39,160",
//         "end": "00:00:40,160",
//         "translatedText": "Hãy đi cùng nhau",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\hay-di-cung-nhau1747960212641.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\hay-di-cung-nhau1747960212641.mp3",
//         "audioDuration1": 1272,
//         "isGenerated1": true
//     },
//     {
//         "index": 22,
//         "id": 22,
//         "text": "你要远离",
//         "startTime": 40.16,
//         "endTime": 41.1,
//         "start": "00:00:40,160",
//         "end": "00:00:41,100",
//         "translatedText": "Bạn phải tránh xa",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-tranh-xa1747960789365.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-tranh-xa1747960789365.mp3",
//         "audioDuration1": 1135,
//         "isGenerated1": true
//     },
//     {
//         "index": 23,
//         "id": 23,
//         "text": "试图接近你的陌生人",
//         "startTime": 41.1,
//         "endTime": 42.68,
//         "start": "00:00:41,100",
//         "end": "00:00:42,680",
//         "translatedText": "Những người lạ cố tình tiếp cận bạn",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\nhung-nguoi-la-co-tinh-t1747960213896.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\nhung-nguoi-la-co-tinh-t1747960213896.mp3",
//         "audioDuration1": 1765,
//         "isGenerated1": true
//     },
//     {
//         "index": 24,
//         "id": 24,
//         "text": "一定要提高防范意识",
//         "startTime": 42.68,
//         "endTime": 45.32,
//         "start": "00:00:42,680",
//         "end": "00:00:45,320",
//         "translatedText": "Nhất định phải nâng cao ý thức phòng ngừa",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\nhat-dinh-phai-nang-cao-1747960231447.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\nhat-dinh-phai-nang-cao-1747960231447.mp3",
//         "audioDuration1": 2045,
//         "isGenerated1": true
//     },
//     {
//         "index": 25,
//         "id": 25,
//         "text": "保护好你自己的人身安全",
//         "startTime": 45.32,
//         "endTime": 47.8,
//         "start": "00:00:45,320",
//         "end": "00:00:47,800",
//         "translatedText": "Bảo vệ an toàn cá nhân của chính mình",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\bao-ve-an-toan-ca-nhan-c1747960234148.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\bao-ve-an-toan-ca-nhan-c1747960234148.mp3",
//         "audioDuration1": 2160,
//         "isGenerated1": true
//     },
//     {
//         "index": 26,
//         "id": 26,
//         "text": "你要记住",
//         "startTime": 47.8,
//         "endTime": 48.96,
//         "start": "00:00:47,800",
//         "end": "00:00:48,960",
//         "translatedText": "Bạn phải nhớ kỹ",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-nho-ky1747960285018.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\ban-phai-nho-ky1747960285018.mp3",
//         "audioDuration1": 1224,
//         "isGenerated1": true
//     },
//     {
//         "index": 27,
//         "id": 27,
//         "text": "我们的生命只有一次",
//         "startTime": 48.96,
//         "endTime": 50.78,
//         "start": "00:00:48,960",
//         "end": "00:00:50,780",
//         "translatedText": "Mạng sống chúng ta chỉ có một lần",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\mang-song-chung-ta-chi-c1747960215660.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\mang-song-chung-ta-chi-c1747960215660.mp3",
//         "audioDuration1": 1920,
//         "isGenerated1": true
//     },
//     {
//         "index": 28,
//         "id": 28,
//         "text": "所以一定要好好保护自己",
//         "startTime": 50.78,
//         "endTime": 53.82,
//         "start": "00:00:50,780",
//         "end": "00:00:53,820",
//         "translatedText": "Vì vậy nhất định phải tự bảo vệ mình thật tốt",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-vay-nhat-dinh-phai-tu1747960216460.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\vi-vay-nhat-dinh-phai-tu1747960216460.mp3",
//         "audioDuration1": 2328,
//         "isGenerated1": true
//     },
//     {
//         "index": 29,
//         "id": 29,
//         "text": "知道吗",
//         "startTime": 53.82,
//         "endTime": 54.34,
//         "start": "00:00:53,820",
//         "end": "00:00:54,340",
//         "translatedText": "Hiểu chưa?",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\hieu-chua1747960217412.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 1,
//         "audioUrl1": "file://F:\\Footage\\0. any\\_-converted_audio\\hieu-chua1747960217412.mp3",
//         "audioDuration1": 765,
//         "isGenerated1": true
//     },
//     {
//         "index": 30,
//         "id": 30,
//         "text": "知道了",
//         "startTime": 54.34,
//         "endTime": 54.98,
//         "start": "00:00:54,340",
//         "end": "00:00:54,980",
//         "translatedText": "Con hiểu rồi",
//         "status": "translated",
//         "selectedSpeaker": "tts.other.BV075_streaming",
//         "speechRate": 0,
//         "audioUrl": "file://F:\\Footage\\0. any\\_-converted_audio\\con-hieu-roi1747960794644.mp3",
//         "duration": 0,
//         "isGenerated": false,
//         "isVoice": 2,
//         "audioUrl2": "file://F:\\Footage\\0. any\\_-converted_audio\\con-hieu-roi1747960794644.mp3",
//         "audioDuration2": 835,
//         "isGenerated2": true
//     }
// ]
console.log(1);
// const videoPath = 'F:\\Footage\\0.-any\\batcoc\\_-converted.mp4'
console.log(2);

// const srtArray = [
//   {
//     index: 1,
//     id: 1,
//     text: '群演一天一千去不去',
//     startTime: 0,
//     endTime: 1.6800000000000002,
//     start: '00:00:00,000',
//     end: '00:00:01,680',
//     translatedText: 'Diễn viên quần chúng một ngày một nghìn, đi không?',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 2,
//     audioUrl2: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\dien-vien-quan-chung-mot1747995697765.mp3',
//     audioDuration2: 2742.857,
//     isGenerated2: true,
//   },
//   {
//     index: 2,
//     id: 2,
//     text: '又想打我',
//     startTime: 1.6800000000000002,
//     endTime: 3.7,
//     start: '00:00:01,680',
//     end: '00:00:03,700',
//     translatedText: 'Lại định đánh tôi nữa à?',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 1,
//     audioUrl1: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\lai-dinh-danh-toi-nua-a1747995699414.mp3',
//     audioDuration1: 1464,
//     isGenerated1: true,
//   },
//   {
//     index: 3,
//     id: 3,
//     text: '没有这次就是',
//     startTime: 3.7,
//     endTime: 5.5,
//     start: '00:00:03,700',
//     end: '00:00:05,500',
//     translatedText: 'Không có, lần này thì...',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\khong-co-lan-nay-thi1747995700236.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 2,
//     audioUrl2: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\khong-co-lan-nay-thi1747995700236.mp3',
//     audioDuration2: 1828.571,
//     isGenerated2: true,
//   },
//   {
//     index: 4,
//     id: 4,
//     text: '你穿的唇域封一点坐车里就行了',
//     startTime: 5.5,
//     endTime: 8,
//     start: '00:00:05,500',
//     end: '00:00:08,000',
//     translatedText: 'Em bịt kín vùng môi một chút, ngồi trong xe là được rồi',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\em-bit-kin-vung-moi-mot-1747995733997.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 2,
//     audioUrl1: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\em-bit-kin-vung-moi-mot-1747995701697.mp3',
//     audioDuration1: 2768.98,
//     isGenerated1: true,
//     audioUrl2: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\em-bit-kin-vung-moi-mot-1747995733997.mp3',
//     audioDuration2: 3082.449,
//     isGenerated2: true,
//   },
//   {
//     index: 5,
//     id: 5,
//     text: '正规',
//     startTime: 8,
//     endTime: 10.38,
//     start: '00:00:08,000',
//     end: '00:00:10,380',
//     translatedText: 'Nghiêm túc không',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\nghiem-tuc-khong1747995704463.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 1,
//     audioUrl1: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\nghiem-tuc-khong1747995704463.mp3',
//     audioDuration1: 984,
//     isGenerated1: true,
//   },
//   {
//     index: 6,
//     id: 6,
//     text: '哪次不正规啊',
//     startTime: 10.38,
//     endTime: 13,
//     start: '00:00:10,380',
//     end: '00:00:13,000',
//     translatedText: 'Lần nào mà không nghiêm túc chứ?',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\lan-nao-ma-khong-nghiem-1747995705239.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 2,
//     audioUrl2: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\lan-nao-ma-khong-nghiem-1747995705239.mp3',
//     audioDuration2: 1525,
//     isGenerated2: true,
//   },
//   {
//     index: 7,
//     id: 7,
//     text: '坐这个车',
//     startTime: 13.72,
//     endTime: 14.72,
//     start: '00:00:13,720',
//     end: '00:00:14,720',
//     translatedText: 'Ngồi xe này',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\ngoi-xe-nay1747995706442.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 1,
//     audioUrl1: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\ngoi-xe-nay1747995706442.mp3',
//     audioDuration1: 895,
//     isGenerated1: true,
//   },
//   {
//     index: 8,
//     id: 8,
//     text: '对啊',
//     startTime: 14.72,
//     endTime: 16.16,
//     start: '00:00:14,720',
//     end: '00:00:16,160',
//     translatedText: 'Đúng vậy',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\dung-vay1747995707288.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 2,
//     audioUrl2: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\dung-vay1747995707288.mp3',
//     audioDuration2: 744,
//     isGenerated2: true,
//   },
//   {
//     index: 9,
//     id: 9,
//     text: '死人三日前一度谋反',
//     startTime: 16.16,
//     endTime: 21.46,
//     start: '00:00:16,160',
//     end: '00:00:21,460',
//     translatedText: 'Người chết ba ngày trước từng mưu phản',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\nguoi-chet-ba-ngay-truoc1747995707833.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 1,
//     audioUrl1: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\nguoi-chet-ba-ngay-truoc1747995707833.mp3',
//     audioDuration1: 1944,
//     isGenerated1: true,
//   },
//   {
//     index: 10,
//     id: 10,
//     text: '七日五十斩首四重',
//     startTime: 21.46,
//     endTime: 23.58,
//     start: '00:00:21,460',
//     end: '00:00:23,580',
//     translatedText: 'Bảy ngày năm mươi tội chém đầu bốn lần',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\bay-ngay-nam-muoi-toi-ch1747995708665.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 1,
//     audioUrl1: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\bay-ngay-nam-muoi-toi-ch1747995708665.mp3',
//     audioDuration1: 2232,
//     isGenerated1: true,
//   },
//   {
//     index: 11,
//     id: 11,
//     text: '来来来来来来',
//     startTime: 23.58,
//     endTime: 25.2,
//     start: '00:00:23,580',
//     end: '00:00:25,200',
//     translatedText: 'Tới đây Tới đây Tới đây',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\toi-day-toi-day-toi-day1747995709586.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 3,
//     audioUrl3: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\toi-day-toi-day-toi-day1747995709586.mp3',
//     audioDuration3: 1416,
//     isGenerated3: true,
//   },
//   {
//     index: 12,
//     id: 12,
//     text: '别墅里面敞开',
//     startTime: 30,
//     endTime: 43.37,
//     start: '00:00:30,000',
//     end: '00:00:43,370',
//     translatedText: 'Bên trong biệt thự mở cửa rộng',
//     status: 'translated',
//     selectedSpeaker: 'tts.other.BV075_streaming',
//     speechRate: 0,
//     audioUrl: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\ben-trong-biet-thu-mo-cu1747995710374.mp3',
//     duration: 0,
//     isGenerated: false,
//     isVoice: 2,
//     audioUrl2: 'file://I:\\ReviewDao\\con-nho\\1111_audio\\ben-trong-biet-thu-mo-cu1747995710374.mp3',
//     audioDuration2: 1704,
//     isGenerated2: true,
//   },
// ];

console.log(2);
// const videoPath = 'I:\\ReviewDao\\con-nho\\1111.mp4';

console.log(3);
const srtArray = [
    {
        "index": 1,
        "id": 1,
        "text": "明眼一天一天是不是",
        "startTime": 0,
        "endTime": 1.48,
        "start": "00:00:00,000",
        "end": "00:00:01,480",
        "translatedText": "Mắt sáng ngày qua ngày có phải không",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\mat-sang-ngay-qua-ngay-c1748086778787.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\mat-sang-ngay-qua-ngay-c1748086778787.mp3",
        "audioDuration1": 2112,
        "isGenerated1": true
    },
    {
        "index": 2,
        "id": 2,
        "text": "又挨板子",
        "startTime": 2.48,
        "endTime": 3.24,
        "start": "00:00:02,480",
        "end": "00:00:03,240",
        "translatedText": "Lại bị phạt đòn",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\lai-bi-phat-don1748086780449.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\lai-bi-phat-don1748086780449.mp3",
        "audioDuration1": 1175,
        "isGenerated1": true
    },
    {
        "index": 3,
        "id": 3,
        "text": "这次不挨",
        "startTime": 3.56,
        "endTime": 4.28,
        "start": "00:00:03,560",
        "end": "00:00:04,280",
        "translatedText": "Lần này không bị",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\lan-nay-khong-bi1748086781583.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\lan-nay-khong-bi1748086781583.mp3",
        "audioDuration1": 965,
        "isGenerated1": true
    },
    {
        "index": 4,
        "id": 4,
        "text": "这次真躺着播动就行",
        "startTime": 4.28,
        "endTime": 5.72,
        "start": "00:00:04,280",
        "end": "00:00:05,720",
        "translatedText": "Lần này thật sự chỉ cần nằm yên là được",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\lan-nay-that-su-chi-can-1748086782212.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\lan-nay-that-su-chi-can-1748086782212.mp3",
        "audioDuration1": 2112,
        "isGenerated1": true
    },
    {
        "index": 5,
        "id": 5,
        "text": "躺那儿啊",
        "startTime": 6.16,
        "endTime": 7.04,
        "start": "00:00:06,160",
        "end": "00:00:07,040",
        "translatedText": "Nằm đấy à",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\nam-day-a1748086783225.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\nam-day-a1748086783225.mp3",
        "audioDuration1": 835,
        "isGenerated1": true
    },
    {
        "index": 6,
        "id": 6,
        "text": "对啊",
        "startTime": 7.04,
        "endTime": 7.6,
        "start": "00:00:07,040",
        "end": "00:00:07,600",
        "translatedText": "Đúng vậy",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\dung-vay1748086783805.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\dung-vay1748086783805.mp3",
        "audioDuration1": 635,
        "isGenerated1": true
    },
    {
        "index": 7,
        "id": 7,
        "text": "正经吗",
        "startTime": 9.2,
        "endTime": 10.04,
        "start": "00:00:09,200",
        "end": "00:00:10,040",
        "translatedText": "Nghiêm túc không",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\nghiem-tuc-khong1748086784326.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\nghiem-tuc-khong1748086784326.mp3",
        "audioDuration1": 1008,
        "isGenerated1": true
    },
    {
        "index": 8,
        "id": 8,
        "text": "哦 正经的",
        "startTime": 10.04,
        "endTime": 10.68,
        "start": "00:00:10,040",
        "end": "00:00:10,680",
        "translatedText": "Ồ nghiêm túc đấy",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\o-nghiem-tuc-day1748086784985.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\o-nghiem-tuc-day1748086784985.mp3",
        "audioDuration1": 905,
        "isGenerated1": true
    },
    {
        "index": 9,
        "id": 9,
        "text": "你看",
        "startTime": 10.68,
        "endTime": 11.36,
        "start": "00:00:10,680",
        "end": "00:00:11,360",
        "translatedText": "Bạn xem này",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\ban-xem-nay1748086785559.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\ban-xem-nay1748086785559.mp3",
        "audioDuration1": 984,
        "isGenerated1": true
    },
    {
        "index": 10,
        "id": 10,
        "text": "我们拍戏的",
        "startTime": 11.36,
        "endTime": 12.2,
        "start": "00:00:11,360",
        "end": "00:00:12,200",
        "translatedText": "Chúng tôi đang quay phim",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\chung-toi-dang-quay-phim1748086786189.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\chung-toi-dang-quay-phim1748086786189.mp3",
        "audioDuration1": 1440,
        "isGenerated1": true
    },
    {
        "index": 11,
        "id": 11,
        "text": "你什么意思啊",
        "startTime": 21.03,
        "endTime": 21.75,
        "start": "00:00:21,030",
        "end": "00:00:21,750",
        "translatedText": "Ý anh là gì vậy?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\y-anh-la-gi-vay1748086787002.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\y-anh-la-gi-vay1748086787002.mp3",
        "audioDuration1": 1075,
        "isGenerated1": true
    },
    {
        "index": 12,
        "id": 12,
        "text": "你个死渣人",
        "startTime": 22.91,
        "endTime": 23.95,
        "start": "00:00:22,910",
        "end": "00:00:23,950",
        "translatedText": "Đồ khốn nạn chết tiệt!",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\do-khon-nan-chet-tiet1748086787681.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\do-khon-nan-chet-tiet1748086787681.mp3",
        "audioDuration1": 1195,
        "isGenerated1": true
    },
    {
        "index": 13,
        "id": 13,
        "text": "你跟他说什么意思啊",
        "startTime": 23.95,
        "endTime": 24.99,
        "start": "00:00:23,950",
        "end": "00:00:24,990",
        "translatedText": "Anh nói với anh ta có ý gì vậy?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\anh-noi-voi-anh-ta-co-y-1748086788324.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\anh-noi-voi-anh-ta-co-y-1748086788324.mp3",
        "audioDuration1": 1745,
        "isGenerated1": true
    },
    {
        "index": 14,
        "id": 14,
        "text": "不是 宝宝",
        "startTime": 24.99,
        "endTime": 25.59,
        "start": "00:00:24,990",
        "end": "00:00:25,590",
        "translatedText": "Không phải, em yêu",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\khong-phai-em-yeu1748086789287.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\khong-phai-em-yeu1748086789287.mp3",
        "audioDuration1": 1750.2040000000002,
        "isGenerated1": true
    },
    {
        "index": 15,
        "id": 15,
        "text": "你听我",
        "startTime": 25.59,
        "endTime": 25.87,
        "start": "00:00:25,590",
        "end": "00:00:25,870",
        "translatedText": "Anh nghe em nói",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\anh-nghe-em-noi1748086790349.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\anh-nghe-em-noi1748086790349.mp3",
        "audioDuration1": 955,
        "isGenerated1": true
    },
    {
        "index": 16,
        "id": 16,
        "text": "他谁啊",
        "startTime": 25.87,
        "endTime": 26.35,
        "start": "00:00:25,870",
        "end": "00:00:26,350",
        "translatedText": "Anh ta là ai thế?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\anh-ta-la-ai-the1748086790972.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\anh-ta-la-ai-the1748086790972.mp3",
        "audioDuration1": 1175,
        "isGenerated1": true
    },
    {
        "index": 17,
        "id": 17,
        "text": "听我解释",
        "startTime": 26.91,
        "endTime": 27.71,
        "start": "00:00:26,910",
        "end": "00:00:27,710",
        "translatedText": "Nghe em giải thích đã",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\nghe-em-giai-thich-da1748086791712.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\nghe-em-giai-thich-da1748086791712.mp3",
        "audioDuration1": 1320,
        "isGenerated1": true
    },
    {
        "index": 18,
        "id": 18,
        "text": "这是我老家来的妹妹",
        "startTime": 27.71,
        "endTime": 28.87,
        "start": "00:00:27,710",
        "end": "00:00:28,870",
        "translatedText": "Đây là em gái từ quê em lên",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\day-la-em-gai-tu-que-em-1748086792440.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\day-la-em-gai-tu-que-em-1748086792440.mp3",
        "audioDuration1": 2016,
        "isGenerated1": true
    },
    {
        "index": 19,
        "id": 19,
        "text": "你对你老家来的妹妹",
        "startTime": 29.51,
        "endTime": 30.87,
        "start": "00:00:29,510",
        "end": "00:00:30,870",
        "translatedText": "Anh đối xử với em gái từ quê lên như thế à?",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\anh-doi-xu-voi-em-gai-tu1748086793369.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\anh-doi-xu-voi-em-gai-tu1748086793369.mp3",
        "audioDuration1": 2592,
        "isGenerated1": true
    },
    {
        "index": 20,
        "id": 20,
        "text": "你老家来的妹妹是一个",
        "startTime": 30.87,
        "endTime": 31.91,
        "start": "00:00:30,870",
        "end": "00:00:31,910",
        "translatedText": "Em gái từ quê lên của anh là một...",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\em-gai-tu-que-len-cua-an1748086794366.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\em-gai-tu-que-len-cua-an1748086794366.mp3",
        "audioDuration1": 2040,
        "isGenerated1": true
    },
    {
        "index": 21,
        "id": 21,
        "text": "啊",
        "startTime": 32.23,
        "endTime": 34.23,
        "start": "00:00:32,230",
        "end": "00:00:34,230",
        "translatedText": "A",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\a1748086795305.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\a1748086795305.mp3",
        "audioDuration1": 535,
        "isGenerated1": true
    },
    {
        "index": 22,
        "id": 22,
        "text": "不是",
        "startTime": 34.23,
        "endTime": 35.23,
        "start": "00:00:34,230",
        "end": "00:00:35,230",
        "translatedText": "Không phải",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\khong-phai1748086795823.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\khong-phai1748086795823.mp3",
        "audioDuration1": 816,
        "isGenerated1": true
    },
    {
        "index": 23,
        "id": 23,
        "text": "听我解释",
        "startTime": 35.23,
        "endTime": 36.23,
        "start": "00:00:35,230",
        "end": "00:00:36,230",
        "translatedText": "Nghe tôi giải thích",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\nghe-toi-giai-thich1748086796411.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\nghe-toi-giai-thich1748086796411.mp3",
        "audioDuration1": 955,
        "isGenerated1": true
    },
    {
        "index": 24,
        "id": 24,
        "text": "我俩是清白的",
        "startTime": 36.23,
        "endTime": 37.23,
        "start": "00:00:36,230",
        "end": "00:00:37,230",
        "translatedText": "Hai chúng tôi hoàn toàn trong sạch",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\hai-chung-toi-hoan-toan-1748086797003.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\hai-chung-toi-hoan-toan-1748086797003.mp3",
        "audioDuration1": 1608,
        "isGenerated1": true
    },
    {
        "index": 25,
        "id": 25,
        "text": "我对你这么好",
        "startTime": 37.23,
        "endTime": 38.23,
        "start": "00:00:37,230",
        "end": "00:00:38,230",
        "translatedText": "Tôi đối xử tốt với em như vậy",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\toi-doi-xu-tot-voi-em-nh1748086797825.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\toi-doi-xu-tot-voi-em-nh1748086797825.mp3",
        "audioDuration1": 1605,
        "isGenerated1": true
    },
    {
        "index": 26,
        "id": 26,
        "text": "你在外面给我找这么个活的",
        "startTime": 38.23,
        "endTime": 39.23,
        "start": "00:00:38,230",
        "end": "00:00:39,230",
        "translatedText": "Em lại đi tìm cho tôi một cái như thế này ở ngoài",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\em-lai-di-tim-cho-toi-mo1748086798708.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\em-lai-di-tim-cho-toi-mo1748086798708.mp3",
        "audioDuration1": 2505,
        "isGenerated1": true
    },
    {
        "index": 27,
        "id": 27,
        "text": "你",
        "startTime": 39.23,
        "endTime": 40.23,
        "start": "00:00:39,230",
        "end": "00:00:40,230",
        "translatedText": "Em",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\em1748086799617.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\em1748086799617.mp3",
        "audioDuration1": 696,
        "isGenerated1": true
    },
    {
        "index": 28,
        "id": 28,
        "text": "是吧",
        "startTime": 40.23,
        "endTime": 41.23,
        "start": "00:00:40,230",
        "end": "00:00:41,230",
        "translatedText": "Phải không",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\phai-khong1748086800267.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\phai-khong1748086800267.mp3",
        "audioDuration1": 765,
        "isGenerated1": true
    },
    {
        "index": 29,
        "id": 29,
        "text": "我俩是清",
        "startTime": 41.23,
        "endTime": 42.23,
        "start": "00:00:41,230",
        "end": "00:00:42,230",
        "translatedText": "Hai chúng tôi thật sự",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\hai-chung-toi-that-su1748086800867.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\hai-chung-toi-that-su1748086800867.mp3",
        "audioDuration1": 1135,
        "isGenerated1": true
    },
    {
        "index": 30,
        "id": 30,
        "text": "真是清白",
        "startTime": 42.23,
        "endTime": 43.23,
        "start": "00:00:42,230",
        "end": "00:00:43,230",
        "translatedText": "Thật sự trong sạch",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\that-su-trong-sach1748086801588.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\that-su-trong-sach1748086801588.mp3",
        "audioDuration1": 1104,
        "isGenerated1": true
    },
    {
        "index": 31,
        "id": 31,
        "text": "害你",
        "startTime": 43.23,
        "endTime": 45.23,
        "start": "00:00:43,230",
        "end": "00:00:45,230",
        "translatedText": "Hại anh",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\hai-anh1748086802273.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\hai-anh1748086802273.mp3",
        "audioDuration1": 864,
        "isGenerated1": true
    },
    {
        "index": 32,
        "id": 32,
        "text": "害你",
        "startTime": 45.23,
        "endTime": 46.23,
        "start": "00:00:45,230",
        "end": "00:00:46,230",
        "translatedText": "Hại anh",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\hai-anh1748086802822.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\hai-anh1748086802822.mp3",
        "audioDuration1": 864,
        "isGenerated1": true
    },
    {
        "index": 33,
        "id": 33,
        "text": "臭",
        "startTime": 48.23,
        "endTime": 49.23,
        "start": "00:00:48,230",
        "end": "00:00:49,230",
        "translatedText": "Thối",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\thoi1748086803347.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\thoi1748086803347.mp3",
        "audioDuration1": 648,
        "isGenerated1": true
    },
    {
        "index": 34,
        "id": 34,
        "text": "我可以吗",
        "startTime": 50.23,
        "endTime": 51.23,
        "start": "00:00:50,230",
        "end": "00:00:51,230",
        "translatedText": "Tôi có thể không",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\toi-co-the-khong1748086803837.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\toi-co-the-khong1748086803837.mp3",
        "audioDuration1": 1080,
        "isGenerated1": true
    },
    {
        "index": 35,
        "id": 35,
        "text": "我也可以",
        "startTime": 51.23,
        "endTime": 52.23,
        "start": "00:00:51,230",
        "end": "00:00:52,230",
        "translatedText": "Tôi cũng có thể",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\toi-cung-co-the1748086804619.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\toi-cung-co-the1748086804619.mp3",
        "audioDuration1": 975,
        "isGenerated1": true
    },
    {
        "index": 36,
        "id": 36,
        "text": "别说里面苍可",
        "startTime": 53.23,
        "endTime": 54.23,
        "start": "00:00:53,230",
        "end": "00:00:54,230",
        "translatedText": "Đừng nói bên trong có cang có thể",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://I:\\ReviewDao\\con-nho\\2222_audio\\dung-noi-ben-trong-co-ca1748086805202.mp3",
        "duration": 0,
        "isGenerated": false,
        "isVoice": 1,
        "audioUrl1": "file://I:\\ReviewDao\\con-nho\\2222_audio\\dung-noi-ben-trong-co-ca1748086805202.mp3",
        "audioDuration1": 1675,
        "isGenerated1": true
    }
]

// const videoPath = 'I:\\ReviewDao\\con-nho\\2222.mp4';


async function main() {
    // const ffmpegProcessor = new callProcessor.FFmpegVideoProcessor();
    // const res= await processor.processVideo(videoPath, srtArray, {});
    // console.log(res);
    // const ffmpegUtils = new AdvancedFFmpegUtils();
    // const res = await ffmpegUtils.processVideoSegments(videoPath, srtArray, {});
    // console.log(res);

    async function calculateAudioDurations() {
    //   this.progress = 10;
      
      const durations = [];
      const total = srtArray.length;
      
      for (let i = 0; i < total; i++) {
        const item = srtArray[i];
        try {
          const duration = await callProcessor.callProcessor.getAudioDuration(item.audioUrl);
          durations.push({
            ...item,
            audioDuration: duration
          });
          
        //   this.progress = 10 + (i / total) * 30;
        } catch (error) {
          console.error(`Error getting duration for ${item.audioUrl}:`, error);
          durations.push({
            ...item,
            audioDuration: item.endTime - item.startTime
          });
        }
      }
      
    //   this.audioDurations = durations;
      return durations;
    }

    async function startProcessing() {
    //   if (!this.canProcess) return;

    //   this.isProcessing = true;
    //   this.processingResult = null;
    //   this.progress = 0;

      try {
        // Calculate audio durations first
        const srtWithDurations = await calculateAudioDurations();
        
        // this.currentStep = 'Starting video processing...';
        // this.progress = 40;

        // Start main processing
        const result = await callProcessor.callProcessor.processVideo(
          videoPath,
          srtWithDurations
        );

        // this.progress = 100;
        // this.currentStep = 'Processing completed!';
        // this.processingResult = result;
        console.log(result);

      } catch (error) {
        console.error('Processing error:', error);
        // this.processingResult = {
        //   success: false,
        //   error: error.message
        // };
      } finally {
        // this.isProcessing = false;
      }
    }
    // startProcessing();


    // const videoSimplified = new VideoSimplified(srtArray, videoPath, 'F:\\Footage\\0.-any\\batcoc', 'F:\\Footage\\0.-any\\batcoc\\output.mp4');
    // const res = await videoSimplified.process();
    const {data,videoPath} = require('./data');
    const res = await processVideoSimplified(null, videoPath, data, 'I:\\ReviewDao\\bl-dong-vat', 'I:\\ReviewDao\\bl-dong-vat\\output.mp4');
    console.log(res);
  }
  
  main();
  