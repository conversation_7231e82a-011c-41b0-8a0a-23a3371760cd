{"name": "react-remotion", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.8.0", "dependencies": {"@remotion/media-utils": "^4.0.306", "@remotion/player": "^4.0.306", "@remotion/preload": "^4.0.306", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.25.28"}, "devDependencies": {"@tailwindcss/vite": "^4.1.7", "@vitejs/plugin-react": "^4.5.0", "tailwindcss": "^4.1.7", "vite": "^6.3.5"}}