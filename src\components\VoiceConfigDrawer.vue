<template>
  <a-drawer
    :open="visible"
    :title="t('voiceConfig.title')"
    placement="right"
    width="400"
    @close="onClose"
  >
    <div class="space-y-6">
      <!-- Language Selection -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {{ t('voiceConfig.language') }}
        </label>
        <a-select
          v-model:value="config.language"
          class="w-full"
          @change="onLanguageChange"
        >
          <a-select-option value="Tiếng Việt">Tiếng Việt</a-select-option>
          <a-select-option value="English">English</a-select-option>
          <a-select-option value="中文">中文</a-select-option>
        </a-select>
      </div>

      <!-- Voice 1 Configuration -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice1') }}
          </h3>
          <a-checkbox v-model:checked="config.voice1.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice1.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice1.speaker"
              class="w-full"
              :disabled="!config.voice1.enabled"
              @change="testConfig(config.voice1)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                {{ speaker.name }}
              </a-select-option>
            </a-select>
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.speed"
              :min="0"
              :max="5"
              :step="0.1"
              :disabled="!config.voice1.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.volume') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.volume }} Db</span>
            </div>
            <a-slider
              v-model:value="config.voice1.volume"
              :min="-20"
              :max="20"
              :step="1"
              :disabled="!config.voice1.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.pitch"
              :min="-10"
              :max="10"
              :step="1"
              :disabled="!config.voice1.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.rate') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.rate }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.rate"
              :min="0.5"
              :max="2"
              :step="0.1"
              :disabled="!config.voice1.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.trim') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice1.trim }}</span>
            </div>
            <a-slider
              v-model:value="config.voice1.trim"
              :min="0"
              :max="1"
              :step="0.05"
              :disabled="!config.voice1.enabled"
            />
          </div>
        </div>
      </div>

      <!-- Voice 2 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice2') }}
          </h3>
          <a-checkbox v-model:checked="config.voice2.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice2.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice2.speaker"
              class="w-full"
              :disabled="!config.voice2.enabled"
              @change="testConfig(config.voice2)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                {{ speaker.name }}
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 -->
          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.speed"
              :min="0"
              :max="5"
              :step="0.1"
              :disabled="!config.voice2.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.volume') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.volume }} Db</span>
            </div>
            <a-slider
              v-model:value="config.voice2.volume"
              :min="-20"
              :max="20"
              :step="1"
              :disabled="!config.voice2.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.pitch"
              :min="-10"
              :max="10"
              :step="1"
              :disabled="!config.voice2.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.rate') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.rate }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.rate"
              :min="0.5"
              :max="2"
              :step="0.1"
              :disabled="!config.voice2.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.trim') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice2.trim }}</span>
            </div>
            <a-slider
              v-model:value="config.voice2.trim"
              :min="0"
              :max="1"
              :step="0.05"
              :disabled="!config.voice2.enabled"
            />
          </div>
        </div>
      </div>

      <!-- Voice 3 Configuration (similar structure) -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            {{ t('voiceConfig.voice3') }}
          </h3>
          <a-checkbox v-model:checked="config.voice3.enabled"></a-checkbox>
        </div>

        <div class="space-y-4" :class="{ 'opacity-50': !config.voice3.enabled }">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ t('voiceConfig.voiceType') }}
            </label>
            <a-select
              v-model:value="config.voice3.speaker"
              class="w-full"
              :disabled="!config.voice3.enabled"
              @change="testConfig(config.voice3)"
            >
              <a-select-option v-for="speaker in filteredSpeakers" :key="speaker.id" :value="speaker.id">
                {{ speaker.name }}
              </a-select-option>
            </a-select>
          </div>

          <!-- Same sliders as Voice 1 and 2 -->
          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.speed') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.speed }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.speed"
              :min="0"
              :max="5"
              :step="0.1"
              :disabled="!config.voice3.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.volume') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.volume }} Db</span>
            </div>
            <a-slider
              v-model:value="config.voice3.volume"
              :min="-20"
              :max="20"
              :step="1"
              :disabled="!config.voice3.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.pitch') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.pitch }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.pitch"
              :min="-10"
              :max="10"
              :step="1"
              :disabled="!config.voice3.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.rate') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.rate }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.rate"
              :min="0.5"
              :max="2"
              :step="0.1"
              :disabled="!config.voice3.enabled"
            />
          </div>

          <div>
            <div class="flex justify-between mb-1">
              <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ t('voiceConfig.trim') }}
              </label>
              <span class="text-sm text-gray-500">{{ config.voice3.trim }}</span>
            </div>
            <a-slider
              v-model:value="config.voice3.trim"
              :min="0"
              :max="1"
              :step="0.05"
              :disabled="!config.voice3.enabled"
            />
          </div>
        </div>
      </div>

      <!-- Volume Control -->
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-3">
          {{ t('voiceConfig.masterVolume') }}
        </h3>
        <div class="flex justify-between mb-1">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
            {{ t('voiceConfig.volume') }}
          </label>
          <span class="text-sm text-gray-500">{{ config.masterVolume }}%</span>
        </div>
        <a-slider
          v-model:value="config.masterVolume"
          :min="0"
          :max="200"
          :step="5"
        />
      </div>

      <!-- Apply Button -->
      <div class="flex justify-end">
        <a-button type="primary" @click="applyConfig">
          {{ t('common.apply') }}
        </a-button>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, computed, reactive, watch, onMounted } from 'vue';
import { useTTSStore } from '@/stores/ttsStore';
import { useI18n } from '@/i18n/i18n';
import { message } from 'ant-design-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'apply']);
const ttsStore = useTTSStore();
const { t } = useI18n();

// Default voice configuration
const defaultVoiceConfig = {
  language: 'Tiếng Việt',
  voice1: {
    enabled: true,
    speaker: 'tts.other.BV562_streaming',
    speed: 2,
    volume: 0,
    pitch: 0,
    rate: 1,
    trim: 0.1
  },
  voice2: {
    enabled: true,
    speaker: 'tts.other.BV074_streaming',
    speed: 2,
    volume: 0,
    pitch: 0,
    rate: 1,
    trim: 0.1
  },
  voice3: {
    enabled: true,
    speaker: 'tts.other.BV075_streaming',
    speed: 2,
    volume: 0,
    pitch: 0,
    rate: 1,
    trim: 0.1
  },
  masterVolume: 100
};

// Reactive configuration object
const config = reactive({ ...defaultVoiceConfig });

// Filtered speakers based on selected language
const filteredSpeakers = computed(() => {
  console.log(ttsStore.speakers);
  
  return ttsStore.speakers.filter(speaker => {
    // Filter logic based on language
    if (config.language === 'Tiếng Việt') {
      return speaker.name.includes('Việt') || 
             speaker.name.includes('VN') || 
             speaker.name.includes('Vietnamese');
    } else if (config.language === 'English') {
      return speaker.name.includes('EN') || 
             speaker.name.includes('English') || 
             speaker.name.includes('GG-');
    } else if (config.language === '中文') {
      return speaker.name.includes('CN') || 
             speaker.name.includes('Chinese');
    }
    return true;
  });
});

// Watch for language changes to update default speakers
watch(() => config.language, (newLanguage) => {
  if (filteredSpeakers.value.length > 0) {
    config.voice1.speaker = filteredSpeakers.value[0].id;
    config.voice2.speaker = filteredSpeakers.value.length > 1 ? filteredSpeakers.value[1].id : filteredSpeakers.value[0].id;
    config.voice3.speaker = filteredSpeakers.value.length > 2 ? filteredSpeakers.value[2].id : filteredSpeakers.value[0].id;
  }
});

// Handle language change
function onLanguageChange(value) {
  config.language = value;
}

// Close drawer
function onClose() {
  emit('close');
}

// Apply configuration
function applyConfig() {
  console.log('Applying config:', config);
  emit('apply', { ...config });
  message.success(t('voiceConfig.configApplied'));
  emit('close');
}

onMounted(() => {
  // Initialize with current config
  applyConfig();
});


// test listener if config changed
async function testConfig(params) {
  const text =  `Tìm hiểu về các giọng nói tổng hợp khác nhau có sẵn để sử dụng trong Chuyển văn bản thành giọng nói`
      const audio_config= {}
      if(params.pitch > 0) audio_config.pitch_rate = params.pitch
      if(params.speech > 0) audio_config.speech_rate = params.speech
      
      try {
        const response = await electronAPI.generateTTS({
          text,
          speaker: params.speaker,
          typeEngine: ttsStore.typeEngine,
          audio_config
        })

        if (response.success) {
          const audio = new Audio(response.audioUrl);
          audio.play();
        }else{
          message.error('Error generating TTS: ' + response.message);
        }
      } catch (error) {
        console.error('Error generating TTS:', error);
        message.error('Error generating TTS: ' + error.message);
      } finally {
      }
}


</script>
