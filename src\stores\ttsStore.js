import { defineStore } from 'pinia';
import axios from 'axios';
import { message } from 'ant-design-vue';
import { parseSRT } from '../lib/utils';

export const useTTSStore = defineStore('tts', {
  state: () => ({
    cookie: '',//localStorage.getItem('cookie') || '',
    speakers: [],
    selectedSpeaker: 'BV075_streaming',//localStorage.getItem('selectedSpeaker') || 'BV075_streaming',
    workspaceId: '',//localStorage.getItem('workspaceId') || '7417632273850214593',
    tiktokSessionId:'',
    text: '',
    audioUrl: '',
    audioDuration: 0,
    isLoading: false,
    error: null,
    generatedAudios: [],//JSON.parse(localStorage.getItem('generatedAudios') || '[]'),
    srtFile: null,
    srtContent: null,
    srtAudios: [],
    processingStatus: '',
    typeEngine: 'capcut', // 'capcut' or 'volcengine'
    language: 'vi',
    params: {
      speech_rate: 0,
      pitch_rate: 0,
      volume: 1,
      sample_rate: 24000,
    },
    srtLists: [],
    currentSrtList: null,
    activeTab: 'tts',
    openaiKey: '',
    deepseekKey: '',
    geminiKey: '',
    model: 'deepseek-chat',
    terminologyDictionary: {}
  }),
  persist: {
    storage: localStorage,
    pick: ['cookie', 'selectedSpeaker', 'workspaceId', 'generatedAudios','typeEngine', 'srtLists', 'tiktokSessionId', 'openaiKey', 'deepseekKey', 'geminiKey', 'model']
  },
  actions: {
    setCookie(cookie) {
      this.cookie = cookie;
      // localStorage.setItem('cookie', cookie);
      
    },
    
    setSelectedSpeaker(speaker) {
      this.selectedSpeaker = speaker;
      // localStorage.setItem('selectedSpeaker', speaker);
    },
    
    setWorkspaceId(id) {
      this.workspaceId = id;
      // localStorage.setItem('workspaceId', id);
    },
    setTiktokSessionId(id) {
      this.tiktokSessionId = id;
    },
    setText(text) {
      this.text = text;
    },
    setTypeEngine(engine) {
      this.typeEngine = engine;
    },
    updateTTSParams (params) {
      if(params.speech_rate) this.params.speech_rate = params.speech_rate;
      if(params.pitch_rate) this.params.pitch_rate = params.pitch_rate;
      if(params.volume) this.params.volume = params.volume;
      if(params.sample_rate) this.params.sample_rate = params.sample_rate;
    },
    async fetchSpeakers() {
      try {
        const response = await electronAPI.getSpeakers(this.typeEngine); //axios.get('http://localhost:4200/api/tts/speakers');
        
        if (response.success) {
          this.speakers = response.data;
          this.selectedSpeaker = this.speakers[0].id;
        } else {
          message.error('Error fetching speakers: ' + response.message);
        }
      } catch (error) {
        this.error = error.message;
        console.error('Error fetching speakers:', error);
      }
    },
    
    async generateTTS() {
      this.isLoading = true;
      this.error = null;
      const audio_config= {}
      if(this.params.pitch_rate > 0) audio_config.pitch_rate = this.params.pitch_rate
      if(this.params.speech_rate > 0) audio_config.speech_rate = this.params.speech_rate
      
      try {
        const response = await electronAPI.generateTTS(/*//axios.post('http://localhost:4200/api/tts/generate',*/ {
          text: this.text,
          speaker: this.selectedSpeaker,
          workspaceId: this.workspaceId,
          cookie: this.cookie,
          typeEngine: this.typeEngine,
          language: this.language,
          audio_config
        })
        
        if (response.success) {
          this.audioUrl = response.audioUrl;
          this.audioDuration = response.duration;
          
          // Add to generated audios
          const newAudio = {
            id: Date.now(),
            text: this.text,
            speaker: this.selectedSpeaker,
            url: this.audioUrl,
            duration: this.audioDuration,
            timestamp: new Date().toISOString(),
            voice: this.speakers.find(s => s.id === this.selectedSpeaker).name || ''
          };
          
          this.generatedAudios.push(newAudio);
          // localStorage.setItem('generatedAudios', JSON.stringify(this.generatedAudios));
        }else{
          message.error('Error generating TTS: ' + response.message);
        }
      } catch (error) {
        this.error = error.message;
        console.error('Error generating TTS:', error);
        message.error('Error generating TTS: ' + error.message);
      } finally {
        this.isLoading = false;
      }
    },
    
    async processSRTFile(file) {
      this.isLoading = true;
      this.error = null;
      this.srtFile = file;
      this.processingStatus = 'Reading SRT file...';
      
      try {
        // Read the SRT file
        const reader = new FileReader();
        const content = await new Promise((resolve, reject) => {
          reader.onload = (e) => resolve(e.target.result);
          reader.onerror = (e) => reject(e);
          reader.readAsText(file);
        });
        
        this.srtContent = content;
        
        // Parse SRT content
        const srtItems = parseSRT(content);
        this.processingStatus = `Parsed ${srtItems.length} items from SRT file`;
        
        // Generate TTS for each item
        this.srtAudios = [];
        for (let i = 0; i < srtItems.length; i++) {
          const item = srtItems[i];
          this.processingStatus = `Generating TTS for item ${i+1}/${srtItems.length}...`;
          
          try {
            const response = await electronAPI.processSRT(/*//axios.post('http://localhost:4200/api/tts/generate',*/ {
              text: item.text,
              speaker: this.selectedSpeaker,
              workspaceId: this.workspaceId,
              cookie: this.cookie
            });
            
            if (response.success) {
              this.srtAudios.push({
                ...item,
                audioUrl: response.audioUrl,
                duration: response.duration
              });
            }
          } catch (error) {
            console.error(`Error generating TTS for item ${i+1}:`, error);
          }
        }
        
        this.processingStatus = 'All TTS items generated successfully';
      } catch (error) {
        this.error = error.message;
        console.error('Error processing SRT file:', error);
      } finally {
        this.isLoading = false;
      }
    },
    // openai, deepseek, gemini set
    setOpenaiKey(key) {
      this.openaiKey = key;
    },
    setDeepseekKey(key) {
      this.deepseekKey = key;
    },
    setGeminiKey(key) {
      this.geminiKey = key;
    },
    setModel(model) {
      this.model = model;
    },
    
    clearGeneratedAudios() {
      this.generatedAudios = [];
      // localStorage.removeItem('generatedAudios');
    },
    
    removeGeneratedAudio(id) {
      this.generatedAudios = this.generatedAudios.filter(audio => audio.id !== id);
      // localStorage.setItem('generatedAudios', JSON.stringify(this.generatedAudios));
    }
  }
});
