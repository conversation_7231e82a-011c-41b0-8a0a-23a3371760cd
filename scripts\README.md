```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
source venv/Scripts/activate
source venv/Scripts/deactivate
```



```bash
pip install -r requirements.txt

# You can download and install ccache from: https://github.com/ccache/ccache/blob/master/doc/INSTALL.md
choco install ccache

```

động vật: --crop 0.04,0.85,0.96,0.97
truyen tranh: --crop 0,0.85,1,1