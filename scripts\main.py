import os
import argparse
import time
from core.subtitle_extractor import VideoSubtitleExtractor
from utils import check_gpu_availability, SUPPORTED_LANGUAGES

def main():
    parser = argparse.ArgumentParser(description="Extract hardcoded subtitles from video.")
    parser.add_argument('--video', type=str, required=True, help="Path to the input video file.")
    parser.add_argument('--output', type=str, required=True, help="Path to save the output .srt file.")
    parser.add_argument('--lang', type=str, default='zh', help="Language code for subtitle extraction (default: zh).")
    parser.add_argument('--frame_rate', type=int, default=5, help="Frame processing rate.")
    parser.add_argument('--threshold', type=float, default=0.7, help="Subtitle confidence threshold.")

    args = parser.parse_args()

    video_path = args.video
    output_path = args.output
    lang_code = args.lang
    frame_rate = args.frame_rate
    confidence_threshold = args.threshold

    if not os.path.isfile(video_path):
        # print(f"[ERROR] Video file not found: {video_path}")
        return

    use_gpu = check_gpu_availability()
    print(f"[INFO] GPU Available: {use_gpu}")
    # print(f"[INFO] Starting subtitle extraction from: {video_path}")

    # Tạo extractor với các tham số
    extractor = VideoSubtitleExtractor(lang=lang_code, use_gpu=use_gpu, video_path=video_path, output_path=output_path)

    # Lấy metadata video
    metadata = extractor.get_video_metadata(video_path)

    start_time = time.time()

    subtitles = extractor.extract_subtitles(
        video_path,
        frame_rate=frame_rate,
        confidence_threshold=confidence_threshold
    )

    end_time = time.time()
    print(f"[INFO] Extraction complete in {end_time - start_time:.2f} seconds.")
    # print(f"[INFO] Saving subtitles to: {output_path}")

    with open(output_path, 'w', encoding='utf-8') as f:
        for i, sub in enumerate(subtitles, 1):
            f.write(f"{i}\n{sub['start_time']} --> {sub['end_time']}\n{sub['text']}\n\n")

    print("[SUCCESS] Subtitles saved successfully.")

if __name__ == "__main__":
    main()
