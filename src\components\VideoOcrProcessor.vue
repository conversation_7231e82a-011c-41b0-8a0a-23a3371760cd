<template>
  <div class="space-y-4">
    <h2 class="text-xl font-semibold">Video OCR Processor</h2>

    <!-- Video upload area -->
    <div v-if="!videoFile">
      <DragDropUpload
        accept="video/*"
        :max-size="10 * 1024 * 1024 * 1024"
        :show-preview="false"
        drag-text="Drag and drop video file here"
        drop-text="Drop video file here"
        click-text="or click to select video"
        @files-selected="handleVideoSelected"
      />
    </div>

    <!-- Video preview and controls -->
    <div v-if="videoFile" class="space-y-4">
      <!-- Video info -->
      <div class="flex justify-between items-center">
        <div class="text-sm flex items-center px-2 py-1 bg-gray-50 rounded-md flex-1 mr-2">
          <div class="font-medium truncate">
            <span class="text-gray-500 mr-1">Video:</span> {{ videoFile.name }}
          </div>
        </div>
        <a-button @click="resetVideo" size="small">Reset</a-button>
      </div>

      <!-- Video preview with crop selection -->
      <div class="relative rounded-md overflow-hidden bg-black">
        <div ref="videoContainer" class="relative">
          <video
            ref="videoRef"
            :src="videoUrl"
            class="max-w-full max-h-[400px] object-contain mx-auto"
            @loadedmetadata="handleVideoLoaded"
            @timeupdate="handleTimeUpdate"
            @click="togglePlayPause"
          ></video>

          <!-- Selection rectangle overlay -->
          <div
            v-if="showSelectionOverlay"
            class="absolute top-0 left-0 w-full h-full cursor-crosshair"
            @mousedown="startSelection"
            @mousemove="updateSelection"
            @mouseup="endSelection"
            @mouseleave="cancelSelection"
          >
            <!-- Selection rectangle -->
            <div
              v-if="isSelecting || selectionRect.width > 0"
              class="absolute border-2 border-red-500  bg-opacity-20"
              :style="{
                left: `${selectionRect.x}px`,
                top: `${selectionRect.y}px`,
                width: `${selectionRect.width}px`,
                height: `${selectionRect.height}px`
              }"
            ></div>
          </div>
        </div>

        <!-- Custom video controls -->
        <div class="mt-2 p-2 bg-gray-900 rounded-md">
          <div class="flex items-center space-x-2">
            <!-- Play/Pause button -->
            <button
              @click="togglePlayPause"
              class="px-2 py-1 bg-gray-600 hover:bg-gray-500 rounded"
            >
              {{ isPlaying ? '⏸️ Pause' : '▶️ Play' }}
            </button>

            <!-- Progress bar -->
            <div class="flex-grow h-2 bg-gray-300 rounded-full overflow-hidden cursor-pointer" @click="seekVideo">
              <div
                class="h-full bg-blue-500"
                :style="{ width: `${videoProgress}%` }"
              ></div>
            </div>

            <!-- Time display -->
            <div class="text-xs text-gray-600">
              {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
            </div>

            <!-- Volume control -->
            <div class="flex items-center space-x-1">
              <button @click="toggleMute" class="px-1 py-1 bg-gray-600 hover:bg-gray-300 rounded">
                {{ isMuted ? '🔇' : '🔊' }}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                v-model="volume"
                class="w-16"
              />
            </div>
          </div>
        </div>

        <!-- Selection controls -->
        <div class="mt-2 flex justify-between items-center">
          <div>
            <a-button
              size="small"
              @click="toggleSelectionOverlay"
              :type="showSelectionOverlay ? 'primary' : 'default'"
            >
              {{ showSelectionOverlay ? 'Cancel Selection' : 'Select Region' }}
            </a-button>
            <a-button
              v-if="selectionRect.width > 0"
              size="small"
              @click="clearSelection"
              class="ml-2"
            >
              Clear Selection
            </a-button>
          </div>
          <div v-if="selectionRect.width > 0" class="text-xs text-gray-500">
            Selected region: {{ formatCropValue() }}
          </div>
        </div>
      </div>

      <!-- OCR settings -->
      <div class="space-y-4 p-4 border border-gray-200 rounded-md">
        <h3 class="text-lg font-medium">OCR Settings</h3>

        <!-- Language selection -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Language</label>
          <a-select v-model:value="language" style="width: 100%">
            <a-select-option value="zh">Chinese</a-select-option>
            <a-select-option value="en">English</a-select-option>
            <a-select-option value="ja">Japanese</a-select-option>
            <a-select-option value="ko">Korean</a-select-option>
            <!-- <a-select-option value="vi">Vietnamese</a-select-option> -->
          </a-select>
        </div>

        <!-- Frame rate -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Frame Rate (frames per second)</label>
          <a-slider
            v-model:value="frameRate"
            :min="1"
            :max="10"
            :step="1"
          />
          <div class="text-xs text-gray-500">
            Higher values capture more text but take longer to process
          </div>
        </div>

        <!-- Batch script path -->
        <div class="space-y-1">
          <label class="text-sm font-medium">OCR Script Path (Optional)</label>
          <div class="flex space-x-2">
            <a-input v-model:value="batchScriptPath" placeholder="Path to OCR batch script" />
            <a-button @click="selectBatchScript">Browse</a-button>
          </div>
          <p class="text-xs text-gray-500">Leave empty to use built-in OCR</p>
        </div>
      </div>

      <!-- Process buttons -->
      <div class="flex justify-end space-x-2">
        <a-button
          v-if="isProcessing && currentProcessId"
          danger
          @click="stopProcessing"
        >
          Stop Processing
        </a-button>
        <a-button
          type="primary"
          @click="processVideo"
          :loading="isProcessing"
          :disabled="!videoFile || isProcessing"
        >
          {{ isProcessing ? 'Processing...' : 'Extract Text with OCR' }}
        </a-button>
      </div>

      <!-- Processing status -->
      <div v-if="isProcessing" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
          <p class="text-blue-700">{{ processingStatus }}</p>
        </div>
      </div>

      <!-- Result -->
      <div v-if="srtFilePath" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
        <div class="flex justify-between items-center">
          <div>
            <p class="text-green-700 font-medium">OCR processing complete!</p>
            <p class="text-sm text-gray-600">SRT file saved to: {{ srtFilePath }}</p>
          </div>
          <div class="space-x-2">
            <a-button type="primary" @click="openSrtFile">Open SRT</a-button>
            <a-button @click="importToSrtTable">Import to SRT Table</a-button>
          </div>
        </div>
      </div>

      <!-- Error -->
      <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <p class="text-red-700">Error: {{ error }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onBeforeUnmount } from 'vue';
import { message } from 'ant-design-vue';
import DragDropUpload from './DragDropUpload.vue';
import { useSRTStore } from '../stores/srtStore';

const srtStore = useSRTStore();

// Reactive state
const videoFile = ref(null);
const videoUrl = ref(null);
const videoRef = ref(null);
const videoContainer = ref(null);
const language = ref('zh');
const frameRate = ref(3);
const batchScriptPath = ref('');
const isProcessing = ref(false);
const processingStatus = ref('');
const currentProcessId = ref(null);
const srtFilePath = ref(null);
const error = ref(null);

// Video player state
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const volume = ref(1);
const isMuted = ref(false);
const videoProgress = ref(0);

// Selection state
const showSelectionOverlay = ref(false);
const isSelecting = ref(false);
const selectionStart = ref({ x: 0, y: 0 });
const selectionRect = ref({ x: 0, y: 0, width: 0, height: 0 });
const videoDimensions = ref({ width: 0, height: 0 });

// Handle video selection
const handleVideoSelected = (files) => {
  if (files && files.length > 0) {
    handleVideoFile(files[0]);
  }
};

// Process video file
const handleVideoFile = (file) => {
  // Check file type
  if (!file.type.startsWith('video/')) {
    message.error('Invalid file type. Please select a video file.');
    return;
  }

  // Revoke old URL to avoid memory leaks
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  videoFile.value = file;
  videoUrl.value = `file://${file.path}`;

  // Reset result and error
  srtFilePath.value = null;
  error.value = null;

  // Reset selection
  clearSelection();
};

// Handle video loaded metadata
const handleVideoLoaded = () => {
  if (videoRef.value) {
    // Store video dimensions for crop calculations
    videoDimensions.value = {
      width: videoRef.value.videoWidth,
      height: videoRef.value.videoHeight
    };

    // Set video duration
    duration.value = videoRef.value.duration;

    // Reset video player state
    currentTime.value = 0;
    isPlaying.value = false;
    videoProgress.value = 0;

    // Set default volume
    videoRef.value.volume = volume.value;
  }
};

// Handle video time update
const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;
    videoProgress.value = (currentTime.value / duration.value) * 100;
  }
};

// Toggle play/pause
const togglePlayPause = () => {
  if (!videoRef.value) return;

  if (isPlaying.value) {
    videoRef.value.pause();
  } else {
    videoRef.value.play();
  }

  isPlaying.value = !isPlaying.value;
};

// Seek video to position
const seekVideo = (event) => {
  if (!videoRef.value) return;

  const rect = event.target.getBoundingClientRect();
  const pos = (event.clientX - rect.left) / rect.width;
  videoRef.value.currentTime = pos * duration.value;
};

// Toggle mute
const toggleMute = () => {
  if (!videoRef.value) return;

  videoRef.value.muted = !videoRef.value.muted;
  isMuted.value = videoRef.value.muted;
};

// Watch volume changes
watch(volume, (newVolume) => {
  if (videoRef.value) {
    videoRef.value.volume = newVolume;
  }
});

// Format time (seconds to MM:SS format)
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00';

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// Reset video
const resetVideo = () => {
  // Stop video playback if playing
  if (videoRef.value && isPlaying.value) {
    videoRef.value.pause();
    isPlaying.value = false;
  }

  // Revoke object URL
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  // Reset video state
  videoFile.value = null;
  videoUrl.value = null;
  srtFilePath.value = null;
  error.value = null;
  currentTime.value = 0;
  duration.value = 0;
  videoProgress.value = 0;

  // Reset selection
  clearSelection();

  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopProcessing();
  } else {
    currentProcessId.value = null;
  }
};

// Toggle selection overlay
const toggleSelectionOverlay = () => {
  showSelectionOverlay.value = !showSelectionOverlay.value;
  if (!showSelectionOverlay.value) {
    isSelecting.value = false;
  }
};

// Start selection
const startSelection = (event) => {
  if (!showSelectionOverlay.value) return;

  isSelecting.value = true;

  // Get mouse position relative to video container
  const rect = videoContainer.value.getBoundingClientRect();
  selectionStart.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  };

  // Initialize selection rectangle
  selectionRect.value = {
    x: selectionStart.value.x,
    y: selectionStart.value.y,
    width: 0,
    height: 0
  };
};

// Update selection
const updateSelection = (event) => {
  if (!isSelecting.value) return;

  // Get mouse position relative to video container
  const rect = videoContainer.value.getBoundingClientRect();
  const currentX = event.clientX - rect.left;
  const currentY = event.clientY - rect.top;

  // Calculate rectangle dimensions
  const width = currentX - selectionStart.value.x;
  const height = currentY - selectionStart.value.y;

  // Update selection rectangle
  selectionRect.value = {
    x: width >= 0 ? selectionStart.value.x : currentX,
    y: height >= 0 ? selectionStart.value.y : currentY,
    width: Math.abs(width),
    height: Math.abs(height)
  };
};

// End selection
const endSelection = () => {
  isSelecting.value = false;
};

// Cancel selection
const cancelSelection = () => {
  if (isSelecting.value) {
    isSelecting.value = false;
  }
};

// Clear selection
const clearSelection = () => {
  selectionRect.value = { x: 0, y: 0, width: 0, height: 0 };
  isSelecting.value = false;
};

// Format crop value for OCR
const formatCropValue = () => {
  if (!videoContainer.value || selectionRect.value.width === 0) return '';

  const containerWidth = videoContainer.value.clientWidth;
  const containerHeight = videoContainer.value.clientHeight;

  // Calculate normalized coordinates (0-1 range)
  const x1 = Math.max(0, Math.min(1, selectionRect.value.x / containerWidth));
  const y1 = Math.max(0, Math.min(1, selectionRect.value.y / containerHeight));
  const x2 = Math.max(0, Math.min(1, (selectionRect.value.x + selectionRect.value.width) / containerWidth));
  const y2 = Math.max(0, Math.min(1, (selectionRect.value.y + selectionRect.value.height) / containerHeight));

  return `${x1.toFixed(2)},${y1.toFixed(2)},${x2.toFixed(2)},${y2.toFixed(2)}`;
};

// Get crop value for OCR
const getCropValue = () => {
  if (selectionRect.value.width === 0) return null;
  return formatCropValue();
};

// Select batch script
const selectBatchScript = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openFile'],
      filters: [
        { name: 'Batch Files', extensions: ['bat', 'cmd', 'sh', 'py'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });

    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      batchScriptPath.value = result.filePaths[0];
    }
  } catch (err) {
    console.error('Error selecting batch script:', err);
    message.error('Error selecting batch script');
  }
};

// Process video with OCR
const processVideo = async () => {
  if (!videoFile.value) return;

  isProcessing.value = true;
  error.value = null;
  srtFilePath.value = null;
  currentProcessId.value = null;

  try {
    processingStatus.value = 'Processing video with OCR...';

    // Get crop value if selection exists
    const cropValue = getCropValue();

    // Use batch script if provided, otherwise use built-in OCR
    let result;

    if (batchScriptPath.value) {
      // Use batch script
      result = await window.electronAPI.runOcrBatch({
        videoPath: videoFile.value.path,
        batchPath: batchScriptPath.value
      });
    } else {
      // Use built-in OCR
      result = await window.electronAPI.processVideoOcr({
        videoPath: videoFile.value.path,
        lang: language.value,
        frameRate: frameRate.value,
        crop: cropValue
      });
    }

    if (result.success) {
      currentProcessId.value = result.processId;
      srtFilePath.value = result.outputPath;

      // Wait for processing to complete
      await checkProcessStatus();

      message.success('OCR processing completed successfully');
    } else {
      throw new Error(result.error || 'Failed to process video with OCR');
    }
  } catch (err) {
    error.value = err.message || 'Unknown error occurred';
    message.error('Error processing video: ' + error.value);
  } finally {
    isProcessing.value = false;
    processingStatus.value = '';
  }
};

// Stop processing
const stopProcessing = async () => {
  if (!currentProcessId.value) return;

  try {
    const result = await window.electronAPI.stopProcess(currentProcessId.value);

    if (result.success) {
      message.success('Processing stopped');
      isProcessing.value = false;
      currentProcessId.value = null;
    } else {
      message.error('Failed to stop processing: ' + (result.error || 'Unknown error'));
    }
  } catch (err) {
    message.error('Error stopping process: ' + (err.message || 'Unknown error'));
  }
};

// Check process status periodically
const checkProcessStatus = async () => {
  if (!currentProcessId.value || !isProcessing.value) return;

  try {
    const result = await window.electronAPI.getActiveProcesses();

    if (result.success) {
      // Check if our process is still running
      const isRunning = result.processes.some(p => p.processId === currentProcessId.value);

      if (!isRunning) {
        // Process completed
        return;
      } else {
        // Check again after 2 seconds
        await new Promise(resolve => setTimeout(resolve, 2000));
        return await checkProcessStatus();
      }
    }
  } catch (err) {
    console.error('Error checking process status:', err);
    // Continue checking even if there's an error
    await new Promise(resolve => setTimeout(resolve, 2000));
    return await checkProcessStatus();
  }
};

// Open SRT file
const openSrtFile = async () => {
  if (srtFilePath.value) {
    await window.electronAPI.openFile(srtFilePath.value);
  }
};

// Import to SRT Table
const importToSrtTable = async () => {
  if (!srtFilePath.value) return;

  try {
    // Read the SRT file
    const result = await window.electronAPI.readFile({ filePath: srtFilePath.value });

    if (result.success) {
      // Create a pseudo-file object from the content
      const fileName = srtFilePath.value.split(/[\/\\]/).pop(); // Get filename without path
      const pseudoFile = {
        name: fileName,
        type: 'application/x-subrip',
        content: result.content,
        path: srtFilePath.value
      };

      // Import to SRT Table
      await srtStore.processSrtFile(pseudoFile);

      message.success('Imported to SRT Table');
    } else {
      throw new Error(result.error || 'Failed to read SRT file');
    }
  } catch (err) {
    message.error('Error importing to SRT Table: ' + (err.message || 'Unknown error'));
  }
};

// Clean up on component unmount
onBeforeUnmount(() => {
  // Stop video playback if playing
  if (videoRef.value && isPlaying.value) {
    videoRef.value.pause();
  }

  // Revoke object URL
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopProcessing();
  }
});
</script>
