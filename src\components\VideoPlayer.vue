<template>
  <div class="video-player" :class="videoClass">
    <video ref="video" :src="src" @loadedmetadata="handleVideoLoaded" @timeupdate="handleTimeUpdate"
      @click="togglePlayPause"></video>
  </div>
  <!-- Custom video controls -->
  <div class="mt-2 p-1 bg-gray-900 rounded-md">
    <div class="flex items-center space-x-2">
      <!-- Play/Pause button -->
      <button @click="togglePlayPause" class="px-2 py-1 bg-gray-900 hover:bg-gray-800 rounded">
        {{ isPlaying ? '⏸️' : '▶️' }}
      </button>

      <!-- Progress bar container -->
      <div class="flex-grow h-2 bg-gray-500 rounded-full overflow-hidden cursor-pointer relative" 
           ref="progressContainer"
           @click="seekVideo"
           @mousedown="startDragging"
           @mousemove="onDrag"
           @mouseup="stopDragging"
           @mouseleave="stopDragging">
        <div class="h-full bg-blue-500 pointer-events-none" :style="{ width: `${videoProgress}%` }"></div>
        <!-- Draggable handle -->
        <div class="absolute top-0 w-3 h-2 bg-white rounded-full transform -translate-x-1/2 cursor-pointer"
             :style="{ left: `${videoProgress}%` }"
             @mousedown.stop="startDragging"></div>
      </div>

      <!-- Time display -->
      <div class="text-xs text-gray-600">
        {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
      </div>

      <!-- Volume control -->
      <div class="flex items-center space-x-1">
        <button @click="toggleMute" class="px-1 py-1 bg-gray-900 hover:bg-gray-800 rounded">
          {{ isMuted ? '🔇' : '🔊' }}
        </button>
        <input type="range" min="0" max="1" step="0.1" v-model="volume" class="w-16" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    src: {
      type: String,
      required: true
    },
    size: {
      type: String,
      default: '48'
    }
  },
  data() {
    return {
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      volume: 1,
      isMuted: false,
      videoProgress: 0,
      videoDimensions: {
        width: 0,
        height: 0
      },
      isDragging: false,
      wasPlayingBeforeDrag: false
    };
  },
  mounted() {
    this.$refs.video.addEventListener('timeupdate', this.handleTimeUpdate);
    // Add global event listeners for dragging
    document.addEventListener('mousemove', this.onGlobalDrag);
    document.addEventListener('mouseup', this.onGlobalMouseUp);
  },
  computed: {
    videoClass() {
      return `w-${this.size}`;
    }
  },
  watch: {
    volume(newVolume) {
      if (this.$refs.video) {
        this.$refs.video.volume = newVolume;
      }
    }
  },
  beforeUnmount() {
    this.$refs.video.removeEventListener('timeupdate', this.handleTimeUpdate);
    document.removeEventListener('mousemove', this.onGlobalDrag);
    document.removeEventListener('mouseup', this.onGlobalMouseUp);
  },
  methods: {
    handleTimeUpdate() {
      if (!this.isDragging) {
        this.$emit('timeupdate', this.$refs.video.currentTime);
        this.currentTime = this.$refs.video.currentTime;
        this.videoProgress = this.duration > 0 ? (this.currentTime / this.duration) * 100 : 0;
      }
    },
    formatTime(seconds) {
      if (!seconds || isNaN(seconds)) return '00:00';

      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },
    handleVideoLoaded(event) {
      this.$emit('loadedmetadata', event);
      if (this.$refs.video) {
        // Store video dimensions for crop calculations
        this.videoDimensions = {
          width: this.$refs.video.videoWidth,
          height: this.$refs.video.videoHeight
        };

        // Set video duration
        this.duration = this.$refs.video.duration;

        // Reset video player state
        this.currentTime = 0;
        this.isPlaying = false;
        this.videoProgress = 0;

        // Set default volume
        this.$refs.video.volume = this.volume;
      }
    },
    togglePlayPause() {
      if (!this.$refs.video) return;

      if (this.isPlaying) {
        this.$refs.video.pause();
      } else {
        this.$refs.video.play();
      }

      this.isPlaying = !this.isPlaying;
    },
    toggleMute() {
      if (!this.$refs.video) return;

      this.$refs.video.muted = !this.$refs.video.muted;
      this.isMuted = this.$refs.video.muted;
    },
    seekVideo(event) {
      if (!this.$refs.video || !this.$refs.progressContainer || this.duration === 0) return;

      // Get the progress container element (not the clicked element)
      const progressContainer = this.$refs.progressContainer;
      const rect = progressContainer.getBoundingClientRect();
      
      // Calculate position relative to the progress container
      const pos = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));
      const newTime = pos * this.duration;
      
      // Update video time
      this.$refs.video.currentTime = newTime;
      this.currentTime = newTime;
      this.videoProgress = pos * 100;
    },
    startDragging(event) {
      event.preventDefault();
      this.isDragging = true;
      this.wasPlayingBeforeDrag = this.isPlaying;
      
      // Pause video while dragging for smoother experience
      if (this.isPlaying) {
        this.$refs.video.pause();
      }
      
      // Immediately update position
      this.updateDragPosition(event);
    },
    onDrag(event) {
      if (this.isDragging) {
        this.updateDragPosition(event);
      }
    },
    onGlobalDrag(event) {
      if (this.isDragging) {
        this.updateDragPosition(event);
      }
    },
    onGlobalMouseUp() {
      if (this.isDragging) {
        this.stopDragging();
      }
    },
    stopDragging() {
      if (!this.isDragging) return;
      
      this.isDragging = false;
      
      // Resume playing if it was playing before drag
      if (this.wasPlayingBeforeDrag) {
        this.$refs.video.play();
        this.isPlaying = true;
      }
    },
    updateDragPosition(event) {
      if (!this.$refs.video || !this.$refs.progressContainer || this.duration === 0) return;

      const rect = this.$refs.progressContainer.getBoundingClientRect();
      const pos = Math.max(0, Math.min(1, (event.clientX - rect.left) / rect.width));
      const newTime = pos * this.duration;
      
      // Update video time and progress
      this.$refs.video.currentTime = newTime;
      this.currentTime = newTime;
      this.videoProgress = pos * 100;
    }
  }
};
</script>

<style scoped>
.video-player {
  margin: 0 auto;
}

/* Ensure smooth dragging */
.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:active {
  cursor: grabbing;
}
</style>