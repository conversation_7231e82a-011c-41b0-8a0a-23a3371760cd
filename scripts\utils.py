import paddleocr.tools.infer.utility as utility
import numpy as np
from typing import Dict, <PERSON><PERSON>
import os
import paddle
from types import SimpleNamespace

SUPPORTED_LANGUAGES = {
    'en': 'English',
    'zh': 'Chinese (Simplified)', 
    'ja': 'Japanese',
    'ko': 'Korean',
    'ar': 'Arabic',
}

def check_gpu_availability():
    """Kiểm tra xem GPU có sẵn cho PaddlePaddle không"""
    try:
        return paddle.is_compiled_with_cuda() and paddle.device.is_compiled_with_cuda()
    except:
        return False

def get_language_paths(lang: str) -> Tuple[str, str]:
    """
    Get model and dictionary paths for specified language
    
    Args:
        lang: Language code ('en', 'zh', etc)
        
    Returns:
        Tuple of (model_dir_path, dict_path)
        
    Raises:
        ValueError: If language not supported
    """
    if lang not in SUPPORTED_LANGUAGES:
        supported = ', '.join(SUPPORTED_LANGUAGES.keys())
        raise ValueError(f"Language '{lang}' not supported. Use one of: {supported}")
        
    base_path = "weights"
    model_dir = os.path.join(base_path, "rec", lang)
    dict_path = os.path.join(model_dir, f"{lang}_dict.txt")


    # Validate paths exist
    if not os.path.exists(model_dir):
        raise ValueError(f"Model directory not found: {model_dir}")
    if not os.path.exists(dict_path):
        raise ValueError(f"Dictionary file not found: {dict_path}")
        
    return model_dir, dict_path
def init_args(lang="zh", use_gpu=False, video_path=None, output_path=None):
    print("init_args", lang, use_gpu, video_path, output_path)
    # Tạo đối tượng cấu hình thủ công thay vì sử dụng utility.parse_args()
    args = SimpleNamespace()
    args.use_gpu = use_gpu
    args.warmup = True
    args.lang = lang

    # Thiết lập các đường dẫn mô hình và từ điển
    model_dir, dict_path = get_language_paths(lang)
    args.det_model_dir = "weights/det"
    args.rec_model_dir = model_dir
    args.rec_char_dict_path = dict_path

    # Các tham số khác cần thiết cho PaddleOCR
    args.use_xpu = False
    args.use_npu = False
    args.ir_optim = True
    args.use_tensorrt = False
    args.min_subgraph_size = 15
    args.precision = "fp32"
    args.gpu_mem = 500
    args.image_dir = ""
    args.det_algorithm = "DB"
    args.det_limit_side_len = 960
    args.det_limit_type = "max"
    args.det_db_thresh = 0.3
    args.det_db_box_thresh = 0.6
    args.det_db_unclip_ratio = 1.5
    args.max_batch_size = 10
    args.use_dilation = False
    args.det_db_score_mode = "fast"
    args.rec_algorithm = "SVTR_LCNet"
    args.rec_image_shape = "3, 48, 320"
    args.rec_batch_num = 6
    args.max_text_length = 25
    args.use_space_char = True
    args.drop_score = 0.5
    args.use_angle_cls = False
    args.enable_mkldnn = False
    args.cpu_threads = 10
    args.use_pdserving = False
    args.benchmark = False
    args.show_log = True
    args.use_onnx = False
    args.det_box_type = "quad"
    args.use_mlu = False
    args.use_gcu = False
    args.return_word_box = False

    print("args", args)
    return args

def sorted_boxes(dt_boxes):
    """
    Sort detected text boxes from top to bottom, left to right
    """
    sorted_boxes = sorted(dt_boxes, key=lambda x: (
        np.mean(x[:, 1]),  # Sort by vertical position
        np.mean(x[:, 0])   # Then by horizontal position
    ))
    return sorted_boxes

def filter_center_bottom_bboxes(dt_boxes, img_height, img_width,
                              vertical_ratio=0.6,
                              horizontal_ratio=0.8,
                              min_width_ratio=0.1,
                              max_height_ratio=0.15):
    """
    Enhanced filter for subtitle bounding boxes
    """
    filtered_boxes = []
    for box in dt_boxes:
        # Box dimensions
        box_height = max(box[:, 1]) - min(box[:, 1])
        box_width = max(box[:, 0]) - min(box[:, 0])
        box_center_y = np.mean(box[:, 1])
        
        # Filter conditions
        vertical_condition = box_center_y > (img_height * vertical_ratio)
        width_condition = (box_width < img_width * horizontal_ratio and 
                         box_width > img_width * min_width_ratio)
        height_condition = box_height < img_height * max_height_ratio
        
        if vertical_condition and width_condition and height_condition:
            filtered_boxes.append(box)
    
    return filtered_boxes