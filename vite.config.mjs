import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    electron({
      entry: {
        main: 'electron/index.js', // tất cả file sẽ được bundle vào đây
        preload: 'electron/preload.js' // preload riêng biệt
      },
      vite: {
        build: {
          outDir: 'electron-build',
          rollupOptions: {
            output: {
              // Tên file rõ ràng
              entryFileNames: (chunk) => {
                if (chunk.name === 'main') return 'main.js'
                if (chunk.name === 'preload') return 'preload.js'
                return '[name].js'
              },
            },
          },
        },
      },
    })

  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 5173,
    strictPort: true,
    watch: {
      // Cho phép theo dõi thư mục cụ thể
      ignored: ['**/*', '!**/electron/**','!**/src/**']
    }
  },
})
