const ffmpeg = require('fluent-ffmpeg');


async function trimAudio (inputFile, duration, outputFile) {
  return new Promise((resolve, reject) => {
    ffmpeg(inputFile)
      .setStartTime(0)
      .setDuration(duration)
      .output(outputFile)
      .on('end', () => {
        console.log('Audio trimmed successfully');
        resolve(outputFile);
      })
      .on('error', (err) => {
        console.error('Error trimming audio:', err);
        reject(err);
      })
      .run();
  });
}
async function getAudioDuration (audioPath) {
  try {
    const audioInfo = await new Promise((resolve, reject) => {
      ffmpeg.ffprobe(audioPath, function (err, metadata) {
        if (err) {
          reject(err);
        } else {
          resolve(metadata);
        }
      });
    });
    const duration = parseFloat(audioInfo.format.duration);
    return duration;

  } catch (error) {
    console.error("Error fetching audio duration:", error);
    throw error;
  }
}
const getVideoInfo = async (event, filePath) => {
  if (!filePath) {
    throw new Error("No input specified");
  }

  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(new Error(`FFprobe error: ${err.message}`));
        return;
      }

      if (!metadata || !metadata.streams) {
        reject(new Error("Invalid video file or no metadata found"));
        return;
      }

      const videoStream = metadata.streams.find(
        (stream) => stream.codec_type === "video"
      );
      if (!videoStream) {
        reject(new Error("No video stream found in file"));
        return;
      }

      // Parse frame rate
      let fps = 0;
      if (videoStream.r_frame_rate) {
        const [num, den] = videoStream.r_frame_rate.split("/");
        fps = parseFloat(num) / parseFloat(den);
      }

      // Calculate total frames
      let totalFrames = parseInt(videoStream.nb_frames) || 0;
      if (!totalFrames && metadata.format.duration) {
        totalFrames = Math.ceil(fps * metadata.format.duration);
      }

      resolve({
        width: videoStream.width || null,
        height: videoStream.height || null,
        fps: fps,
        duration: metadata.format.duration || null,
        total_frames: totalFrames
      });
    });
  });
};



module.exports = {
    trimAudio,
    getAudioDuration,
    getVideoInfo
}