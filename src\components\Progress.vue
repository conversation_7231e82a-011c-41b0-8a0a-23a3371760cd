<template>
  <div
    v-if="ttsStore.currentSrtList?.items"
    ref="draggableBox"
    @mousedown="startDrag"
    :style="style"
    class="fixed left-4 dark:bg-gray-800 shadow-lg rounded-lg p-2 z-50 w-80 cursor-move"
    :class="isHide ? 'h-8' : ''"
  >
    <div class="flex justify-between items-center mb-1">
      <h3 class="text-md font-medium">Process {{ countAll }} items</h3>
      <!-- nút ẩn xuống -->
      <button @click="hide" class="text-sm text-gray-500 hover:text-gray-700">
        <ChevronDown size="16" v-if="!isHide" />
        <ChevronUp size="16" v-else />
      </button>



    </div>
    <VideoPlayer
        v-if="!isHide && ttsStore.currentSrtList"
        ref="videoPlayer"
        :src="ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')"
        @timeupdate="handleVideoTimeUpdate"
        size="50"
        />
    <div class="flex gap-1">
        Dịch <a-progress :percent="translatedText" :format="(percent) => `${percent}%`" :percentPosition="{ align: 'start', type: 'inner' }" :size="[60,10]"  />
    </div>
    <div class="flex gap-1">
        TTS <a-progress :percent="audioProgress" :format="(percent) => `${percent}%`" :percentPosition="{ align: 'start', type: 'inner' }" :size="[60,10]" strokeColor="#B7EB8F" />
    </div>
  </div>
</template>
<script setup>
import { ref, watch, reactive, computed, onBeforeUnmount } from 'vue'
import { useI18n } from '@/i18n/i18n';
import { useTTSStore } from '@/stores/ttsStore';
import { Progress } from 'ant-design-vue';
import { ChevronDown, ChevronUp } from 'lucide-vue-next';
import VideoPlayer from './VideoPlayer.vue';
import {state } from '@/lib/state';

const { t } = useI18n()
const ttsStore = useTTSStore()

const audioProgress = ref(0)
const translatedText = ref(0)
const isHide = ref(false)
const countAll = ref(0)
const videoPlayer = ref(null)


state.videoPlayer = videoPlayer

watch(() => ttsStore.currentSrtList?.items, (val) => {
  if (val) {
    const translated = val.filter(item => item.status === 'translated').length;
    const total = val.length;
    translatedText.value = Math.floor((translated / total) * 100);
    const generated = val.filter(item => item.isVoice && item.isVoice > 0 && item[`isGenerated${item.isVoice}`]).length;
    audioProgress.value = Math.floor((generated / total) * 100);
    countAll.value = total;
  }
})

// === Drag logic with bottom ===
const position = reactive({ bottom: 4, left: 4 }); // 16px = left-4
const isDragging = ref(false);
let offset = { x: 0, y: 0 };
const boxHeight = ref(0);
const draggableBox = ref(null);

const style = computed(() => ({
  bottom: `${position.bottom}px`,
  left: `${position.left}px`,
  position: 'fixed',
}));

function startDrag(e) {
  isDragging.value = true;
  boxHeight.value = draggableBox.value.offsetHeight;
  offset.x = e.clientX - position.left;
  offset.y = window.innerHeight - e.clientY - position.bottom;
  window.addEventListener('mousemove', onDrag);
  window.addEventListener('mouseup', stopDrag);
}

function onDrag(e) {
  if (!isDragging.value) return;
  position.left = e.clientX - offset.x;
  position.bottom = window.innerHeight - e.clientY - offset.y;
}

function stopDrag() {
  isDragging.value = false;
  window.removeEventListener('mousemove', onDrag);
  window.removeEventListener('mouseup', stopDrag);
}

onBeforeUnmount(() => {
  window.removeEventListener('mousemove', onDrag);
  window.removeEventListener('mouseup', stopDrag);
});

function hide() {
  isHide.value = !isHide.value;
}

function handleVideoTimeUpdate(time) {
  // playCurrentTime.value = time;
  const currentSubtitle = ttsStore.currentSrtList?.items.find(item => item.startTime <= time && item.endTime >= time);
  if (currentSubtitle) {
    state.currentPlayingSubtitleId = currentSubtitle.id;
  } else {
    state.currentPlayingSubtitleId = null;
  }
}


</script>
