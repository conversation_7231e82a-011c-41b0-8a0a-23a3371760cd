<script setup>
import { ref, computed } from 'vue';
import { useTTSStore } from '../stores/ttsStore';

const ttsStore = useTTSStore();
const text = ref(ttsStore.text);
// const selectedSpeaker = ref(ttsStore.selectedSpeaker);

const speakers = computed(() => ttsStore.speakers);
const isLoading = computed(() => ttsStore.isLoading);
const error = computed(() => ttsStore.error);
const audioUrl = computed(() => ttsStore.audioUrl);

async function generateTTS() {
  if (!text.value) return;
  ttsStore.setText(text.value);
  ttsStore.setSelectedSpeaker(ttsStore.selectedSpeaker);
  await ttsStore.generateTTS();
}

function formatDuration(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

function downloadAudio(){
  const a = document.createElement('a');
  a.href = audioUrl.value;
  a.download = `tts-audio-${Date.now()}.mp3`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}
</script>

<template>
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Text to Speech</h2>
    
    <div class="space-y-4">
      <div>
        <label for="speaker" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Voice
        </label>
        <a-select
          id="speaker"
          v-model:value="ttsStore.selectedSpeaker"
          class="w-full mt-1"
          placeholder="Select a voice"
        >
          <a-select-option v-for="(speaker, index) in speakers" :key="index" :value="speaker.id">
            {{ speaker.name }}
          </a-select-option>
        </a-select>
      </div>
      
      <div>
        <label for="text" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Text
        </label>
        <a-textarea
          id="text"
          v-model:value="text"
          placeholder="Enter text to convert to speech"
          :rows="6"
          class="mt-1"
        />
      </div>
      
      <div class="flex justify-end">
        <a-button type="primary" @click="generateTTS" :loading="isLoading">
          Generate Speech
        </a-button>
      </div>
      
      <div v-if="error" class="mt-4 p-3 bg-red-100 text-red-700 rounded">
        {{ error }}
      </div>
      
      <div v-if="audioUrl" class="mt-4">
        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">Generated Audio</h3>
        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded">
          <audio controls class="w-full" :src="audioUrl"></audio>
          <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            Duration: {{ formatDuration(ttsStore.audioDuration) }}
          </div>
          <div class="mt-2 flex space-x-2">
            <a-button type="default" size="small" :href="audioUrl" target="_blank">
              Open in New Tab
            </a-button>
            <a-button type="default" size="small" @click="downloadAudio">
              Download
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
