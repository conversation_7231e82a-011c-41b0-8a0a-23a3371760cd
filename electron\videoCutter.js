const path = require('path');
const fs = require('fs');
const { execWithLog } = require('./utils');
const { spawn } = require('child_process');

/**
 * Get video duration using ffprobe
 * @param {string} videoPath - Path to the video file
 * @returns {Promise<number>} - Duration in seconds
 */
async function getVideoDuration(videoPath) {
  return new Promise((resolve, reject) => {
    const ffprobe = spawn('ffprobe', [
      '-v', 'error',
      '-show_entries', 'format=duration',
      '-of', 'default=noprint_wrappers=1:nokey=1',
      videoPath
    ]);
    
    let output = '';
    
    ffprobe.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    ffprobe.stderr.on('data', (data) => {
      console.error('ffprobe stderr:', data.toString());
    });
    
    ffprobe.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`ffprobe process exited with code ${code}`));
        return;
      }
      
      const duration = parseFloat(output.trim());
      if (isNaN(duration)) {
        reject(new Error('Failed to parse video duration'));
        return;
      }
      
      resolve(duration);
    });
  });
}

/**
 * Cut a segment from a video
 * @param {Object} options - Cutting options
 * @param {string} options.inputPath - Path to the input video
 * @param {string} options.outputDir - Directory to save the output
 * @param {string} options.startTime - Start time in HH:MM:SS format
 * @param {string} options.duration - Duration in HH:MM:SS format
 * @param {number} options.partNumber - Part number for output filename
 * @param {string} options.videoCodec - Video codec to use (default: h264_nvenc)
 * @param {string} options.audioBitrate - Audio bitrate in kbps (default: 192)
 * @param {string} options.processId - Process ID for tracking (optional)
 * @returns {Promise<Object>} - Result object
 */
async function cutVideoSegment(event,{
  inputPath,
  outputDir,
  startTime,
  duration,
  partNumber = 0,
  videoCodec = 'h264_nvenc',
  audioBitrate = '192',
  processId = null
}) {
  if (!inputPath || !fs.existsSync(inputPath)) {
    return { 
      success: false, 
      error: "Input video file does not exist" 
    };
  }

  try {
    
    // Create output directory if it doesn't exist
    outputDir = path.dirname(inputPath);
    if (!fs.existsSync(outputDir)) {
      // get inputPath
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Generate output filename
    const { name, ext } = path.parse(inputPath);
    const outputPath = path.join(outputDir, `${name}-part${partNumber}${ext}`);
    
    // Prepare ffmpeg arguments
    const ffmpegArgs = [
      '-y',  // Overwrite output file if it exists
      '-i', inputPath,
      '-ss', startTime,
      '-t', duration
    ];
    
    // Add video codec settings
    if (videoCodec === 'h264_nvenc') {
      // NVIDIA GPU encoding
      ffmpegArgs.push(
        '-c:v', 'h264_nvenc',
        '-preset', 'p5',
        '-rc', 'vbr',
        '-cq', '19'
      );
    } else {
      // CPU encoding
      ffmpegArgs.push(
        '-c:v', 'libx264',
        '-preset', 'medium'
      );
    }
    
    // Add audio settings
    ffmpegArgs.push(
      '-c:a', 'aac',
      '-b:a', `${audioBitrate}k`,
      outputPath
    );
    
    console.log(`Cutting video segment: ${startTime} for ${duration}`);
    console.log(`Output: ${outputPath}`);
    
    // Execute ffmpeg command
    const result = await execWithLog(null, 'ffmpeg', ffmpegArgs, processId);
    
    return {
      success: true,
      processId: result.processId,
      outputPath,
      status: 'started'
    };
  } catch (err) {
    console.error("Error cutting video segment:", err.message);
    return { 
      success: false, 
      error: err.message || "Unknown error occurred"
    };
  }
}

/**
 * Split video into equal parts
 * @param {Object} options - Splitting options
 * @param {string} options.inputPath - Path to the input video
 * @param {string} options.outputDir - Directory to save the output
 * @param {number} options.parts - Number of parts to split into
 * @param {string} options.videoCodec - Video codec to use (default: h264_nvenc)
 * @param {string} options.audioBitrate - Audio bitrate in kbps (default: 192)
 * @returns {Promise<Object>} - Result object
 */
async function splitVideoIntoParts({
  inputPath,
  outputDir,
  parts,
  videoCodec = 'h264_nvenc',
  audioBitrate = '192'
}) {
  if (!inputPath || !fs.existsSync(inputPath)) {
    return { 
      success: false, 
      error: "Input video file does not exist" 
    };
  }

  if (!parts || parts < 2) {
    return {
      success: false,
      error: "Number of parts must be at least 2"
    };
  }

  try {
    // Get video duration
    const durationSec = await getVideoDuration(inputPath);
    console.log(`Video duration: ${durationSec} seconds`);
    
    // Calculate part duration
    const partDuration = Math.floor(durationSec / parts);
    console.log(`Each part will be ${partDuration} seconds`);
    
    // Process each part
    const results = [];
    
    for (let i = 0; i < parts; i++) {
      const startSec = i * partDuration;
      
      // Format start time and duration as HH:MM:SS
      const startTime = formatTimeHMS(startSec);
      const durationTime = formatTimeHMS(partDuration);
      
      console.log(`Processing part ${i}: start=${startTime}, duration=${durationTime}`);
      
      // Cut the segment
      const result = await cutVideoSegment({
        inputPath,
        outputDir,
        startTime,
        duration: durationTime,
        partNumber: i,
        videoCodec,
        audioBitrate
      });
      
      results.push(result);
      
      if (!result.success) {
        return {
          success: false,
          error: `Failed to process part ${i}: ${result.error}`,
          completedParts: results.filter(r => r.success)
        };
      }
    }
    
    return {
      success: true,
      parts: results
    };
  } catch (err) {
    console.error("Error splitting video:", err.message);
    return { 
      success: false, 
      error: err.message || "Unknown error occurred"
    };
  }
}

/**
 * Format seconds to HH:MM:SS
 * @param {number} seconds - Time in seconds
 * @returns {string} - Formatted time string
 */
function formatTimeHMS(seconds) {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = Math.floor(seconds % 60);
  
  return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
}

module.exports = {
  getVideoDuration,
  cutVideoSegment,
  splitVideoIntoParts,
  formatTimeHMS
};
