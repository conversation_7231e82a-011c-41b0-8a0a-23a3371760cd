{"name": "tts-app", "private": true, "version": "1.0.2", "scripts": {"dev": "chcp 65001 && vite", "build": "vite build", "preview": "vite preview", "electron:dev": "cross-env NODE_ENV=development electron .", "electron:build": "vite build && npm run bytenote && electron-builder", "electron:preview": "vite build && electron .", "start": "concurrently \"npm run dev\" \"npm run electron:dev\"", "bytenote": "node electron/bytenode-build.js", "remotion:dev": "remotion studio", "remotion:build": "remotion bundle", "remotion:upgrade": "remotion upgrade"}, "main": "electron/main.js", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@google/generative-ai": "^0.24.1", "@langchain/core": "^0.3.56", "@langchain/openai": "^0.5.10", "@remotion/bundler": "^4.0.306", "@remotion/media-utils": "^4.0.306", "@remotion/renderer": "^4.0.306", "ant-design-vue": "^4.2.6", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "base64-js": "^1.5.1", "bytenode": "^1.5.7", "cheerio": "^1.0.0", "clsx": "^2.1.1", "commander": "^14.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "electron-store": "^8.2.0", "express": "^5.1.0", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "fs-extra": "^11.3.0", "js-md5": "^0.8.3", "lucide-vue-next": "^0.511.0", "morgan": "^1.10.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "pinyin": "^4.0.0", "play-sound": "^1.1.6", "postcss": "^8.5.3", "radix-vue": "^1.9.17", "react": "^19.1.0", "remotion": "^4.0.306", "table": "^6.9.0", "tabulate": "^1.0.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "uuid": "^11.1.0", "vite-plugin-electron": "^0.29.0", "vue": "^3.5.13"}, "devDependencies": {"@remotion/cli": "^4.0.306", "@remotion/tailwind-v4": "^4.0.306", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/vite": "^4.1.7", "@types/react": "^19.1.5", "@vitejs/plugin-vue": "^5.2.3", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^31.7.7", "electron-builder": "^26.0.12", "vite": "^6.3.5"}}