import { ref, computed, watch, inject, provide } from 'vue';
import { locales, translateWithParams, getNestedValue } from './index';

const LOCALE_STORAGE_KEY = 'subtitle_translator_locale';
const DEFAULT_LOCALE = 'vi';


const I18N_SYMBOL = Symbol('i18n');

export function createI18nProvider(defaultLocale = DEFAULT_LOCALE) {
  const locale = ref(defaultLocale);

  // Khởi tạo từ localStorage
  const saved = localStorage.getItem(LOCALE_STORAGE_KEY);
  if (saved && Object.keys(locales).includes(saved)) {
    locale.value = saved;
  }

  const setLocale = (newLocale) => {
    locale.value = newLocale;
    localStorage.setItem(LOCALE_STORAGE_KEY, newLocale);
  };

  const t = (key, params) => {
    const value = getNestedValue(locales[locale.value], key);
    if (value === undefined) {
      console.warn(`Translation key "${key}" not found for locale "${locale.value}"`);
      return key;
    }
    return params ? translateWithParams(value, params) : value;
  };

  const formatParams = (text, params) => {
    return params ? translateWithParams(text, params) : text;
  };

  const context = {
    locale: locale.value,
    setLocale,
    t,
    translations: locales[locale.value],
    formatParams
  };

  provide(I18N_SYMBOL, context);
  return context;
}

export function useI18n() {
  const context = inject(I18N_SYMBOL);
  if (!context) {
    throw new Error('useI18n must be used within a provider');
  }
  return context;
}
