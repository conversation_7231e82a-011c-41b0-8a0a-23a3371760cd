const { execWithLog } = require('../utils');
const path = require('path');

async function demucs(event, { fileInput }) {
  const type = 'video-task';
  const outputDirectory = path.dirname(fileInput);
  const baseName = path.basename(fileInput, path.extname(fileInput));
  const outputDir = path.join(outputDirectory, 'htdemucs', baseName);
  event?.sender?.send(type, { data: 'Running Demucs...', code: 0 });
  const vocalsFile = path.join(outputDir, 'vocals.wav');
  const noVocalsFile = path.join(outputDir, 'no_vocals.wav');

  try {
    const args = ['--two-stems', 'vocals', fileInput, '-o', outputDirectory];

    const result = await execWithLog.bind({ type: type })(event, 'demucs', args);

    if (!result.success) {
      event?.sender?.send(type, { data: `Demucs error: ${result}`, code: 1 });
      return { success: false, error: result };
    }

    event?.sender?.send(type, { data: 'Demucs done', code: 0 });
    const res = { success: true, outputPath: outputDirectory, processId: result.processId, vocalsFile, noVocalsFile };
    console.log('Demucs done', res);
    return res;
  } catch (err) {
    console.error('Demucs error:', err);
    event?.sender?.send(type, { data: `Demucs error: ${err.message}`, code: 1 });
    return { success: false, error: err.message };
  }
}

module.exports = demucs;

if (require.main === module) {
  const [, , fileInput] = process.argv;

  demucs(null, {
    fileInput,
  });
}
