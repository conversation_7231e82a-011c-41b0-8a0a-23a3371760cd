<template>
  <div class="video-preview-section">
    <h3>Video Preview</h3>

    <!-- Video Player -->
    <div class="video-container">
      <VideoPlayer
        ref="videoPlayer"
        :src="videoSrc"
        @timeupdate="onVideoTimeUpdate"
        @loadedmetadata="onVideoLoaded"
        size="full h-full"
        class="preview-video"
      />

      <!-- show text animation preview -->
      <div v-if="renderOptions.showText" class="text-preview">
        <p
          class="text-preview-content"
          :style="{
            fontSize: `${getEffectiveFontSize()}px`,
            color: renderOptions.textColor,
            opacity: renderOptions.textOpacity / 100,
            transform: renderOptions.assOptions?.rotation !== undefined ? `rotate(${renderOptions.assOptions.rotation}deg)` : 'none',
            textAlign: getTextAlignment(),
          }"
        >
          {{ renderOptions.textValue }}
        </p>
      </div>

      <!-- show subtitle preview -->
      <div v-if="renderOptions.showSubtitle" class="subtitle-preview">
        <p
          class="subtitle-preview-content"
          :style="{
            position: 'absolute',
            fontSize: `${getEffectiveSubtitleFontSize()}px`,
            color: renderOptions.subtitleTextColor,
            backgroundColor: renderOptions.subtitleBackgroundColor === 'transparent' ? 'rgba(0,0,0,0.0)' : renderOptions.subtitleBackgroundColor,
            textAlign: getTextAlignment(),
            fontWeight: renderOptions.subtitleBold ? 'bold' : 'normal',
            _border: `2px solid ${renderOptions.subtitleBorderColor}`,
            padding: '2px',
            textShadow:
              renderOptions.subtitleBorderColor === 'transparent'
                ? 'none'
                : `
                  -1px -1px 0 ${renderOptions.subtitleBorderColor},
                  1px -1px 0 ${renderOptions.subtitleBorderColor},
                  -1px  1px 0 ${renderOptions.subtitleBorderColor},
                  1px  1px 0 ${renderOptions.subtitleBorderColor},
                  -2px  0px 0 ${renderOptions.subtitleBorderColor},
                  2px  0px 0 ${renderOptions.subtitleBorderColor},
                  0px -2px 0 ${renderOptions.subtitleBorderColor},
                  0px  2px 0 ${renderOptions.subtitleBorderColor}
                `,
                top: `${getAssPositionY()}px`,
                left: `${getAssPositionX()}px`,
                transform: renderOptions.assOptions?.rotation !== undefined ? `rotate(${renderOptions.assOptions.rotation}deg)` : 'none',
          }"
        >
          {{ ttsStore.currentSrtList?.items[0]?.translatedText || ttsStore.currentSrtList?.items[0]?.text || 'Sample Subtitle Text' }}
        </p>
      </div>

      <!-- Drawing Canvas Overlay -->
      <canvas
        ref="drawingCanvas"
        class="drawing-canvas"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @mouseleave="handleMouseLeave"
        @click="handleCanvasClick"
      />
    </div>

    <!-- Drawing Controls -->
    <div class="drawing-controls">
      <a-space class="flex-wrap">
        <a-button
          :type="drawingMode === 'blur' ? 'primary' : 'default'"
          @click="setDrawingMode('blur')"
          size="small"
        >
          <blur-outlined />
          Blur Area
        </a-button>

        <a-button
          :type="drawingMode === 'delogo' ? 'primary' : 'default'"
          @click="setDrawingMode('delogo')"
          size="small"
        >
          <delete-outlined />
          Remove Logo
        </a-button>

        <a-button
          :type="drawingMode === 'subtitle' ? 'primary' : 'default'"
          @click="setDrawingMode('subtitle')"
          size="small"
        >
          <font-colors-outlined />
          Remove Subtitle
        </a-button>

        <a-button
          :type="drawingMode === 'select' ? 'primary' : 'default'"
          @click="setDrawingMode('select')"
          size="small"
        >
          <drag-outlined />
          Select/Move
        </a-button>

        <a-button @click="clearDrawings" size="small">
          <clear-outlined />
          Clear All
        </a-button>

        <a-button @click="undoLastDrawing" size="small">
          <undo-outlined />
          Undo
        </a-button>

        <a-button @click="initializeDefaultBlurArea" size="small" type="dashed">
          <font-colors-outlined />
          Add Default Area
        </a-button>
      </a-space>

      <div v-if="selectedArea" class="selected-area-info">
        <a-divider />
        <p><strong>Selected:</strong> {{ selectedArea.type }} Area</p>
        <a-row :gutter="8">
          <a-col :span="6">
            <a-form-item label="X" size="small">
              <a-input-number
                v-model:value="selectedArea.x"
                size="small"
                @change="redrawCanvas"
                :min="0"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="Y" size="small">
              <a-input-number
                v-model:value="selectedArea.y"
                size="small"
                @change="redrawCanvas"
                :min="0"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="Width" size="small">
              <a-input-number
                v-model:value="selectedArea.width"
                size="small"
                @change="redrawCanvas"
                :min="10"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="Height" size="small">
              <a-input-number
                v-model:value="selectedArea.height"
                size="small"
                @change="redrawCanvas"
                :min="10"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- Video Info Debug -->
    <div class="video-info-debug" v-if="videoWidth && videoHeight">
      <h4>Video Info:</h4>
      <p>
        Video Dimensions: {{ videoWidth }} x {{ videoHeight }}<br>
        Display Dimensions: {{ videoPlayer?.$refs?.video?.offsetWidth || 0 }} x {{ videoPlayer?.$refs?.video?.offsetHeight || 0 }}<br>
        Scale: {{ (videoWidth / (videoPlayer?.$refs?.video?.offsetWidth || 1)).toFixed(2) }} x {{ (videoHeight / (videoPlayer?.$refs?.video?.offsetHeight || 1)).toFixed(2) }}
      </p>

      <!-- ASS Options Debug -->
      <div v-if="renderOptions?.assOptions">
        <h5>ASS Coordinate System:</h5>
        <p>
          <strong>ASS Resolution:</strong> {{ ASS_PLAY_RES_X }}x{{ ASS_PLAY_RES_Y }}<br>
          <strong>Video Resolution:</strong> {{ videoWidth }}x{{ videoHeight }}<br>
          <strong>Display Size:</strong> {{ videoPlayer?.$refs?.video?.offsetWidth || 0 }}x{{ videoPlayer?.$refs?.video?.offsetHeight || 0 }}<br>
          <strong>Scale Factor:</strong> {{ (videoWidth / ASS_PLAY_RES_X).toFixed(3) }}x{{ (videoHeight / ASS_PLAY_RES_Y).toFixed(3) }}
        </p>
        <p>
          <strong>Slider Values:</strong> {{ renderOptions.assOptions.posX || 0 }}%, {{ renderOptions.assOptions.posY || 0 }}%<br>
          <strong>ASS Coordinates:</strong> pos({{ getAssOptionsForVideo().assPos?.x || 'N/A' }},{{ getAssOptionsForVideo().assPos?.y || 'N/A' }})<br>
          <strong>Video Coordinates:</strong> {{ getAssOptionsForVideo().pos?.x || 'N/A' }}, {{ getAssOptionsForVideo().pos?.y || 'N/A' }}<br>
          <strong>Display Position:</strong> {{ getAssPositionX().toFixed(0) }}, {{ getAssPositionY().toFixed(0) }}px<br>
          <strong>Rotation:</strong> {{ renderOptions.assOptions.rotation || 0 }}°<br>
          <strong>Alignment:</strong> {{ renderOptions.assOptions.align || 2 }}<br>
          <strong>Font Size:</strong> ASS={{ getAssOptionsForVideo().assFontSize || 'Auto' }}, Video={{ getAssOptionsForVideo().fontSize || 'Auto' }}
        </p>
      </div>
    </div>

    <!-- Blur Areas List -->
    <div class="blur-areas-list" v-if="blurAreas.length > 0">
      <h4>Blur/Remove Areas:</h4>
      <a-list size="small" :data-source="blurAreas">
        <template #renderItem="{ item, index }">
          <a-list-item>
            <template #actions>
              <a-button
                size="small"
                type="text"
                danger
                @click="removeBlurArea(index)"
              >
                <delete-outlined />
              </a-button>
            </template>
            <a-list-item-meta>
              <template #title>
                {{ item.type.charAt(0).toUpperCase() + item.type.slice(1) }} Area {{ index + 1 }}
              </template>
              <template #description>
                Display: {{ Math.round(item.x) }}, {{ Math.round(item.y) }} - {{ Math.round(item.width) }} x {{ Math.round(item.height) }}<br>
                Video: {{ Math.round(getVideoCoords(item).x) }}, {{ Math.round(getVideoCoords(item).y) }} - {{ Math.round(getVideoCoords(item).width) }} x {{ Math.round(getVideoCoords(item).height) }}<br>
                Time: {{ item.timeStart }}s - {{ item.timeEnd }}s
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </div>

    <!-- Time Range Controls -->
    <div class="time-controls">
      <h4>Apply to Time Range:</h4>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="Start Time (seconds)">
            <a-input-number
              v-model:value="localTimeRange.start"
              :min="0"
              :max="videoDuration"
              :step="0.1"
              style="width: 100%"
              @change="updateTimeRange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="End Time (seconds)">
            <a-input-number
              v-model:value="localTimeRange.end"
              :min="0"
              :max="videoDuration"
              :step="0.1"
              style="width: 100%"
              @change="updateTimeRange"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-button @click="setCurrentTimeAsStart" size="small" style="margin-right: 8px">
        Set Current as Start
      </a-button>
      <a-button @click="setCurrentTimeAsEnd" size="small">
        Set Current as End
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  DeleteOutlined,
  FontColorsOutlined,
  ClearOutlined,
  UndoOutlined,
  DragOutlined
} from '@ant-design/icons-vue';
import VideoPlayer from './VideoPlayer.vue';
import { useTTSStore } from '@/stores/ttsStore';

const ttsStore = useTTSStore();

// Props
const props = defineProps({
  videoSrc: {
    type: String,
    required: true
  },
  renderOptions: {
    type: Object,
    required: true
  },
  timeRange: {
    type: Object,
    required: true
  },
  videoDuration: {
    type: Number,
    default: 0
  },
  currentTime: {
    type: Number,
    default: 0
  }
});

// Emits
const emit = defineEmits([
  'blur-areas-updated',
  'time-range-updated',
  'video-loaded',
  'video-time-update'
]);

// Video preview refs
const videoPlayer = ref(null);
const drawingCanvas = ref(null);

// Video dimensions for coordinate conversion
const videoWidth = ref(0);
const videoHeight = ref(0);

// Drawing state
const isDrawing = ref(false);
const isResizing = ref(false);
const drawingMode = ref('subtitle'); // 'blur', 'delogo', 'subtitle'
const blurAreas = ref([]);
const currentDrawing = ref(null);
const drawingHistory = ref([]);
const selectedArea = ref(null);
const resizeHandle = ref(null); // 'nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'
const isDragging = ref(false);

// Local time range for two-way binding
const localTimeRange = reactive({
  start: 0,
  end: 0
});

// Watch for prop changes
watch(() => props.timeRange, (newRange) => {
  localTimeRange.start = newRange.start;
  localTimeRange.end = newRange.end;
}, { immediate: true });

// Watch for ASS options changes and emit updates
watch(() => props.renderOptions?.assOptions, () => {
  // Emit updated ASS options when they change
  if (videoWidth.value && videoHeight.value) {
    emitBlurAreasUpdate();
  }
}, { deep: true });

// Initialize canvas on mount
onMounted(() => {
  redrawCanvas();
});

// Coordinate conversion function - converts display coordinates to actual video coordinates
const getVideoCoords = (displayBox) => {
  if (!videoPlayer.value?.$refs?.video) return { x: 0, y: 0, width: 0, height: 0 };

  const videoElement = videoPlayer.value.$refs.video;
  const scaleX = videoWidth.value / videoElement.offsetWidth;
  const scaleY = videoHeight.value / videoElement.offsetHeight;

  return {
    x: displayBox.x * scaleX,
    y: displayBox.y * scaleY,
    width: displayBox.width * scaleX,
    height: displayBox.height * scaleY
  };
};

// Video event handlers
function onVideoLoaded() {
  if (videoPlayer.value?.$refs?.video) {
    const video = videoPlayer.value.$refs.video;

    // Store actual video dimensions
    videoWidth.value = video.videoWidth;
    videoHeight.value = video.videoHeight;

    const duration = video.duration;
    localTimeRange.end = duration;

    emit('video-loaded', video);

    setupCanvas();
    setTimeout(() => {
      initializeDefaultBlurArea();
    }, 800);
  }
}

function onVideoTimeUpdate() {
  if (videoPlayer.value?.$refs?.video) {
    const currentTime = videoPlayer.value.$refs.video.currentTime;
    emit('video-time-update', currentTime);
  }
}

function setupCanvas() {
  if (drawingCanvas.value && videoPlayer.value?.$refs?.video) {
    const canvas = drawingCanvas.value;
    const video = videoPlayer.value.$refs.video;

    canvas.width = video.clientWidth;
    canvas.height = video.clientHeight;
  }
}

// Time range functions
function updateTimeRange() {
  emit('time-range-updated', {
    start: localTimeRange.start,
    end: localTimeRange.end
  });
}

function setCurrentTimeAsStart() {
  localTimeRange.start = props.currentTime;
  updateTimeRange();
}

function setCurrentTimeAsEnd() {
  localTimeRange.end = props.currentTime;
  updateTimeRange();
}

// Initialize default blur area with automatic timeEnd
function initializeDefaultBlurArea() {
  if (props.videoDuration <= 0) {
    message.warning('Please wait for video to load completely');
    return;
  }

  const canvas = drawingCanvas.value;
  if (!canvas) {
    message.warning('Canvas not ready');
    return;
  }

  // If no blur areas exist, create a default subtitle area
  if (blurAreas.value.length === 0) {
    const defaultArea = {
      type: "subtitle",
      x: canvas.width * 0.01, // 1% from left
      y: canvas.height * 0.85, // 85% from top (bottom area)
      width: canvas.width * 0.97, // 97% of video width
      height: canvas.height * 0.1, // 10% of video height
      timeStart: 0,
      timeEnd: props.videoDuration // Automatically use video duration
    };

    blurAreas.value.push(defaultArea);
    console.log('Initialized default blur area:', defaultArea);
    console.log('Video coordinates:', getVideoCoords(defaultArea));
    message.success('Default subtitle area added');
  } else {
    // If areas exist, add a new area based on current drawing mode
    const areaCount = blurAreas.value.length;
    const offset = (areaCount * 20) % 100; // Offset each new area slightly

    const newArea = {
      type: drawingMode.value,
      x: (canvas.width * 0.1) + offset,
      y: (canvas.height * 0.7) + offset,
      width: canvas.width * 0.3,
      height: canvas.height * 0.15,
      timeStart: localTimeRange.start,
      timeEnd: localTimeRange.end || props.videoDuration
    };

    blurAreas.value.push(newArea);
    console.log('Added new area:', newArea);
    console.log('Video coordinates:', getVideoCoords(newArea));
    message.success(`New ${drawingMode.value} area added`);
  }

  // Update existing areas' timeEnd if they don't have proper values
  blurAreas.value.forEach(area => {
    if (!area.timeEnd || area.timeEnd === 0 || area.timeEnd > props.videoDuration) {
      area.timeEnd = props.videoDuration;
    }
  });

  // Emit updated blur areas with video coordinates
  emitBlurAreasUpdate();

  // Redraw canvas to show the new area
  nextTick(() => {
    redrawCanvas();
  });
}

// Emit blur areas update with video coordinates and ASS options
function emitBlurAreasUpdate() {
  const areasWithVideoCoords = blurAreas.value.map(area => ({
    ...area,
    videoCoords: getVideoCoords(area)
  }));

  // Include ASS options for video coordinates
  const assOptionsForVideo = getAssOptionsForVideo();

  emit('blur-areas-updated', {
    areas: areasWithVideoCoords,
    assOptions: assOptionsForVideo
  });
}

// ASS coordinate system constants (like in your ASS file)
const ASS_PLAY_RES_X = 1440;
const ASS_PLAY_RES_Y = 1080;

// ASS position conversion functions
function getAssPositionX() {
  if (!videoPlayer.value?.$refs?.video || !props.renderOptions?.assOptions) return 0;

  const videoElement = videoPlayer.value.$refs.video;
  const posXPercent = props.renderOptions.assOptions.posX || 0;

  // Calculate ASS coordinates like in your example: pos(690,957)
  // Convert percentage (-100 to 100) to ASS coordinate system
  const assX = (ASS_PLAY_RES_X / 2) + (ASS_PLAY_RES_X * posXPercent / 200);

  // Scale ASS coordinates to display size
  const scaleX = videoElement.offsetWidth / ASS_PLAY_RES_X;

  return assX * scaleX;
}

function getAssPositionY() {
  if (!videoPlayer.value?.$refs?.video || !props.renderOptions?.assOptions) return 0;

  const videoElement = videoPlayer.value.$refs.video;
  const posYPercent = props.renderOptions.assOptions.posY || 0;

  // Calculate ASS coordinates like in your example: pos(690,957)
  // Convert percentage (-100 to 100) to ASS coordinate system
  const assY = (ASS_PLAY_RES_Y / 2) + (ASS_PLAY_RES_Y * posYPercent / 200);

  // Scale ASS coordinates to display size
  const scaleY = videoElement.offsetHeight / ASS_PLAY_RES_Y;

  return assY * scaleY;
}

// Get ASS options in video coordinates for FFmpeg processing
function getAssOptionsForVideo() {
  if (!props.renderOptions?.assOptions) return {};

  const assOptions = props.renderOptions.assOptions;
  const videoCoords = {};

  // Convert position from percentage to ASS coordinate system (like pos(690,957))
  // Only add position if it's not at default center (0%, 50% for bottom center)
  const posX = assOptions.posX || 0;
  const posY = assOptions.posY || 0;

  // Check if position is at default center for bottom center alignment (align: 2)
  const isDefaultPosition = (posX === 0 && posY === 50 && (assOptions.align === 2 || assOptions.align === undefined));

  if (!isDefaultPosition && (assOptions.posX !== undefined || assOptions.posY !== undefined)) {
    // Calculate base ASS coordinates
    // Convert percentage (-100 to 100) to ASS coordinate system (1440x1080)
    let assX = (ASS_PLAY_RES_X / 2) + (ASS_PLAY_RES_X * posX / 200);
    let assY = (ASS_PLAY_RES_Y / 2) + (ASS_PLAY_RES_Y * posY / 200);

    // Scale ASS coordinates to actual video resolution
    const scaleX = videoWidth.value / ASS_PLAY_RES_X;
    const scaleY = videoHeight.value / ASS_PLAY_RES_Y;

    videoCoords.pos = {
      x: Math.round(assX * scaleX),
      y: Math.round(assY * scaleY)
    };

    // Also provide raw ASS coordinates for direct ASS file generation
    videoCoords.assPos = {
      x: Math.round(assX),
      y: Math.round(assY)
    };
  }

  // Font size scaling based on video resolution vs ASS resolution
  if (assOptions.fontSize !== undefined && assOptions.fontSize > 0) {
    // Scale font size based on video resolution vs ASS resolution
    const fontScale = Math.min(videoWidth.value / ASS_PLAY_RES_X, videoHeight.value / ASS_PLAY_RES_Y);
    videoCoords.fontSize = Math.round(assOptions.fontSize * fontScale);

    // Also provide raw ASS font size
    videoCoords.assFontSize = assOptions.fontSize;
  }

  if (assOptions.rotation !== undefined) {
    videoCoords.rotation = assOptions.rotation;
  }

  if (assOptions.align !== undefined) {
    videoCoords.align = assOptions.align;
  }

  // Include ASS resolution info for proper scaling
  videoCoords.assResolution = {
    width: ASS_PLAY_RES_X,
    height: ASS_PLAY_RES_Y
  };

  return videoCoords;
}

// Helper functions for preview styling
function getEffectiveFontSize() {
  // Use ASS font size override if specified, otherwise use default
  if (props.renderOptions?.assOptions?.fontSize && props.renderOptions.assOptions.fontSize > 0) {
    return props.renderOptions.assOptions.fontSize / 2; // Scale down for preview
  }
  return props.renderOptions?.fontSize || 24;
}

function getEffectiveSubtitleFontSize() {
  // Use ASS font size override if specified, otherwise use subtitle font size
  if (props.renderOptions?.assOptions?.fontSize && props.renderOptions.assOptions.fontSize > 0) {
    // Scale ASS font size to display size (like in ASS system)
    if (videoPlayer.value?.$refs?.video) {
      const displayScale = videoPlayer.value.$refs.video.offsetHeight / ASS_PLAY_RES_Y;
      return props.renderOptions.assOptions.fontSize * displayScale;
    }
    return props.renderOptions.assOptions.fontSize / 2; // Fallback
  }

  // Use default subtitle font size scaled to display
  if (videoPlayer.value?.$refs?.video) {
    const displayScale = videoPlayer.value.$refs.video.offsetHeight / ASS_PLAY_RES_Y;
    return (props.renderOptions?.subtitleFontSize || 48) * displayScale;
  }

  return (props.renderOptions?.subtitleFontSize || 48) / 2; // Fallback
}

function getTextAlignment() {
  if (!props.renderOptions?.assOptions?.align) return 'center';

  // ASS alignment values to CSS text-align mapping
  const alignmentMap = {
    7: 'left',  // Top Left
    8: 'center', // Top Center
    9: 'right',  // Top Right
    4: 'left',   // Middle Left
    5: 'center', // Middle Center
    6: 'right',  // Middle Right
    1: 'left',   // Bottom Left
    2: 'center', // Bottom Center
    3: 'right'   // Bottom Right
  };

  return alignmentMap[props.renderOptions.assOptions.align] || 'center';
}

// Drawing functions
function setDrawingMode(mode) {
  drawingMode.value = mode;
  if (mode !== 'select') {
    selectedArea.value = null;
    redrawCanvas();
  }
}

function handleMouseDown(e) {
  if (!drawingCanvas.value) return;

  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  if (drawingMode.value === 'select') {
    // Check if clicking on resize handle first
    const handle = getResizeHandle(x, y);
    if (handle && selectedArea.value) {
      startResize(handle, x, y);
      return;
    }

    // Check if clicking inside an existing area
    const area = getAreaAtPoint(x, y);
    if (area) {
      selectedArea.value = area;
      startDrag(x, y);
      redrawCanvas();
      return;
    } else {
      selectedArea.value = null;
      redrawCanvas();
    }
  } else {
    // Start drawing new area
    startDrawing(x, y);
  }
}

function handleMouseMove(e) {
  if (!drawingCanvas.value) return;

  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  // Update cursor based on context
  updateCursor(x, y);

  if (isDrawing.value) {
    continueDrawing(x, y);
  } else if (isResizing.value) {
    continueResize(x, y);
  } else if (isDragging.value) {
    continueDrag(x, y);
  }
}

function handleMouseUp() {
  if (isDrawing.value) {
    stopDrawing();
  } else if (isResizing.value) {
    stopResize();
  } else if (isDragging.value) {
    stopDrag();
  }
}

function handleMouseLeave() {
  handleMouseUp();
}

function handleCanvasClick(e) {
  // Handle single clicks for selection
  if (drawingMode.value === 'select' && !isDrawing.value && !isResizing.value && !isDragging.value) {
    const rect = drawingCanvas.value.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const area = getAreaAtPoint(x, y);
    selectedArea.value = area;
    redrawCanvas();
  }
}

function startDrawing(x, y) {
  isDrawing.value = true;
  currentDrawing.value = {
    type: drawingMode.value,
    x,
    y,
    width: 0,
    height: 0,
    timeStart: localTimeRange.start,
    timeEnd: localTimeRange.end
  };
  drawRectangle();
}

function continueDrawing(x, y) {
  if (!currentDrawing.value) return;

  currentDrawing.value.width = x - currentDrawing.value.x;
  currentDrawing.value.height = y - currentDrawing.value.y;
  drawRectangle();
}

function stopDrawing() {
  if (!isDrawing.value || !currentDrawing.value) return;

  isDrawing.value = false;

  // Only add if rectangle has meaningful size
  if (Math.abs(currentDrawing.value.width) > 10 && Math.abs(currentDrawing.value.height) > 10) {
    // Normalize negative dimensions
    if (currentDrawing.value.width < 0) {
      currentDrawing.value.x += currentDrawing.value.width;
      currentDrawing.value.width = Math.abs(currentDrawing.value.width);
    }
    if (currentDrawing.value.height < 0) {
      currentDrawing.value.y += currentDrawing.value.height;
      currentDrawing.value.height = Math.abs(currentDrawing.value.height);
    }

    blurAreas.value.push({ ...currentDrawing.value });
    drawingHistory.value.push([...blurAreas.value]);

    console.log('Added drawing area:', currentDrawing.value);
    console.log('Video coordinates:', getVideoCoords(currentDrawing.value));

    emitBlurAreasUpdate();
  }

  currentDrawing.value = null;
  redrawCanvas();
}

// Resize functions
let resizeStartPos = { x: 0, y: 0 };
let resizeStartArea = null;

function startResize(handle, x, y) {
  isResizing.value = true;
  resizeHandle.value = handle;
  resizeStartPos = { x, y };
  resizeStartArea = { ...selectedArea.value };
}

function continueResize(x, y) {
  if (!selectedArea.value || !resizeHandle.value) return;

  const deltaX = x - resizeStartPos.x;
  const deltaY = y - resizeStartPos.y;
  const area = selectedArea.value;

  switch (resizeHandle.value) {
    case 'nw': // Top-left
      area.x = resizeStartArea.x + deltaX;
      area.y = resizeStartArea.y + deltaY;
      area.width = resizeStartArea.width - deltaX;
      area.height = resizeStartArea.height - deltaY;
      break;
    case 'ne': // Top-right
      area.y = resizeStartArea.y + deltaY;
      area.width = resizeStartArea.width + deltaX;
      area.height = resizeStartArea.height - deltaY;
      break;
    case 'sw': // Bottom-left
      area.x = resizeStartArea.x + deltaX;
      area.width = resizeStartArea.width - deltaX;
      area.height = resizeStartArea.height + deltaY;
      break;
    case 'se': // Bottom-right
      area.width = resizeStartArea.width + deltaX;
      area.height = resizeStartArea.height + deltaY;
      break;
    case 'n': // Top
      area.y = resizeStartArea.y + deltaY;
      area.height = resizeStartArea.height - deltaY;
      break;
    case 's': // Bottom
      area.height = resizeStartArea.height + deltaY;
      break;
    case 'w': // Left
      area.x = resizeStartArea.x + deltaX;
      area.width = resizeStartArea.width - deltaX;
      break;
    case 'e': // Right
      area.width = resizeStartArea.width + deltaX;
      break;
  }

  // Ensure minimum size
  if (area.width < 10) area.width = 10;
  if (area.height < 10) area.height = 10;

  redrawCanvas();
  emitBlurAreasUpdate();
}

function stopResize() {
  isResizing.value = false;
  resizeHandle.value = null;
}

// Drag functions
let dragStartPos = { x: 0, y: 0 };
let dragStartArea = null;

function startDrag(x, y) {
  isDragging.value = true;
  dragStartPos = { x, y };
  dragStartArea = { ...selectedArea.value };
}

function continueDrag(x, y) {
  if (!selectedArea.value) return;

  const deltaX = x - dragStartPos.x;
  const deltaY = y - dragStartPos.y;

  selectedArea.value.x = dragStartArea.x + deltaX;
  selectedArea.value.y = dragStartArea.y + deltaY;

  redrawCanvas();
  emitBlurAreasUpdate();
}

function stopDrag() {
  isDragging.value = false;
}

// Helper functions
function getAreaAtPoint(x, y) {
  for (let i = blurAreas.value.length - 1; i >= 0; i--) {
    const area = blurAreas.value[i];
    if (x >= area.x && x <= area.x + area.width &&
        y >= area.y && y <= area.y + area.height) {
      return area;
    }
  }
  return null;
}

function getResizeHandle(x, y) {
  if (!selectedArea.value) return null;

  const area = selectedArea.value;
  const handleSize = 8;

  // Corner handles
  if (isNearPoint(x, y, area.x, area.y, handleSize)) return 'nw';
  if (isNearPoint(x, y, area.x + area.width, area.y, handleSize)) return 'ne';
  if (isNearPoint(x, y, area.x, area.y + area.height, handleSize)) return 'sw';
  if (isNearPoint(x, y, area.x + area.width, area.y + area.height, handleSize)) return 'se';

  // Edge handles
  if (isNearPoint(x, y, area.x + area.width/2, area.y, handleSize)) return 'n';
  if (isNearPoint(x, y, area.x + area.width/2, area.y + area.height, handleSize)) return 's';
  if (isNearPoint(x, y, area.x, area.y + area.height/2, handleSize)) return 'w';
  if (isNearPoint(x, y, area.x + area.width, area.y + area.height/2, handleSize)) return 'e';

  return null;
}

function isNearPoint(x, y, targetX, targetY, threshold) {
  return Math.abs(x - targetX) <= threshold && Math.abs(y - targetY) <= threshold;
}

function updateCursor(x, y) {
  if (!drawingCanvas.value) return;

  let cursor = 'default';

  if (drawingMode.value === 'select') {
    const handle = getResizeHandle(x, y);
    if (handle) {
      const cursors = {
        'nw': 'nw-resize', 'ne': 'ne-resize',
        'sw': 'sw-resize', 'se': 'se-resize',
        'n': 'n-resize', 's': 's-resize',
        'w': 'w-resize', 'e': 'e-resize'
      };
      cursor = cursors[handle];
    } else if (getAreaAtPoint(x, y)) {
      cursor = 'move';
    }
  } else {
    cursor = 'crosshair';
  }

  drawingCanvas.value.style.cursor = cursor;
}

function drawRectangle() {
  if (!drawingCanvas.value || !currentDrawing.value) return;

  const canvas = drawingCanvas.value;
  const ctx = canvas.getContext('2d');

  // Clear and redraw all areas
  redrawCanvas();

  // Draw current rectangle being drawn
  ctx.strokeStyle = getDrawingColor(currentDrawing.value.type);
  ctx.fillStyle = getDrawingColor(currentDrawing.value.type, 0.3);
  ctx.lineWidth = 2;
  ctx.setLineDash([5, 5]);

  ctx.fillRect(
    currentDrawing.value.x,
    currentDrawing.value.y,
    currentDrawing.value.width,
    currentDrawing.value.height
  );
  ctx.strokeRect(
    currentDrawing.value.x,
    currentDrawing.value.y,
    currentDrawing.value.width,
    currentDrawing.value.height
  );
}

function redrawCanvas() {
  if (!drawingCanvas.value) return;

  const canvas = drawingCanvas.value;
  const ctx = canvas.getContext('2d');

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // Draw existing blur areas
  blurAreas.value.forEach((area, index) => {
    const isSelected = selectedArea.value === area;

    ctx.strokeStyle = getDrawingColor(area.type);
    ctx.fillStyle = getDrawingColor(area.type, 0.3);
    ctx.lineWidth = isSelected ? 3 : 2;
    ctx.setLineDash(isSelected ? [3, 3] : []);

    ctx.fillRect(area.x, area.y, area.width, area.height);
    ctx.strokeRect(area.x, area.y, area.width, area.height);

    // Add label
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.fillText(
      `${area.type} ${index + 1}`,
      area.x + 5,
      area.y + 15
    );

    // Draw resize handles for selected area
    if (isSelected && drawingMode.value === 'select') {
      drawResizeHandles(ctx, area);
    }
  });
}

function drawResizeHandles(ctx, area) {
  const handleSize = 6;
  const handles = [
    { x: area.x, y: area.y }, // nw
    { x: area.x + area.width, y: area.y }, // ne
    { x: area.x, y: area.y + area.height }, // sw
    { x: area.x + area.width, y: area.y + area.height }, // se
    { x: area.x + area.width/2, y: area.y }, // n
    { x: area.x + area.width/2, y: area.y + area.height }, // s
    { x: area.x, y: area.y + area.height/2 }, // w
    { x: area.x + area.width, y: area.y + area.height/2 } // e
  ];

  ctx.fillStyle = '#1890ff';
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.setLineDash([]);

  handles.forEach(handle => {
    ctx.fillRect(
      handle.x - handleSize/2,
      handle.y - handleSize/2,
      handleSize,
      handleSize
    );
    ctx.strokeRect(
      handle.x - handleSize/2,
      handle.y - handleSize/2,
      handleSize,
      handleSize
    );
  });
}

function getDrawingColor(type, alpha = 1) {
  const colors = {
    blur: `rgba(255, 255, 0, ${alpha})`, // Yellow
    delogo: `rgba(255, 0, 0, ${alpha})`, // Red
    subtitle: `rgba(0, 255, 0, ${alpha})` // Green
  };
  return colors[type] || `rgba(255, 255, 255, ${alpha})`;
}

function clearDrawings() {
  blurAreas.value = [];
  drawingHistory.value = [];
  selectedArea.value = null;
  redrawCanvas();
  emitBlurAreasUpdate();
}

function undoLastDrawing() {
  if (blurAreas.value.length > 0) {
    blurAreas.value.pop();
    redrawCanvas();
    emitBlurAreasUpdate();
  }
}

function removeBlurArea(index) {
  blurAreas.value.splice(index, 1);
  redrawCanvas();
  emitBlurAreasUpdate();
}
</script>

<style scoped>
.video-preview-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

.video-container {
  position: relative;
  margin-bottom: 16px;
  border-radius: 4px;
  overflow: hidden;
}

.preview-video {
  width: 100%;
  height: auto;
  max-height: 300px;
  background: #000;
}

.text-preview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 9;
}

.text-preview-content {
  max-width: 90%;
  word-wrap: break-word;
  text-align: center;
}

.drawing-canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.drawing-controls {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.selected-area-info {
  margin-top: 12px;
}

.video-info-debug {
  margin-bottom: 16px;
  padding: 8px;
  /* background: #f5f5f5; */
  border-radius: 4px;
  font-size: 12px;
}

.video-info-debug h4 {
  margin-bottom: 8px;
  color: #1890ff;
  font-size: 14px;
}

.blur-areas-list {
  margin-bottom: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.time-controls {
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.time-controls h4 {
  margin-bottom: 12px;
  color: #1890ff;
}

.subtitle-preview {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 9;
}

.subtitle-preview-content {
  max-width: 90%;
  word-wrap: break-word;
  text-align: center;
}
</style>
