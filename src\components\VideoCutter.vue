<template>
  <div class="space-y-4">
    <h2 class="text-xl font-semibold">Video Cutter</h2>
    
    <!-- Video upload area -->
    <div v-if="!videoFile">
      <DragDropUpload
        accept="video/*"
        :max-size="10 * 1024 * 1024 * 1024"
        :show-preview="false"
        drag-text="Drag and drop video file here"
        drop-text="Drop video file here"
        click-text="or click to select video"
        @files-selected="handleVideoSelected"
      />
    </div>
    
    <!-- Video preview and controls -->
    <div v-if="videoFile" class="space-y-4">
      <!-- Video info -->
      <div class="flex justify-between items-center">
        <div class="text-sm flex items-center px-2 py-1 bg-gray-50 rounded-md flex-1 mr-2">
          <div class="font-medium truncate">
            <span class="text-gray-500 mr-1">Video:</span> {{ videoFile.name }}
          </div>
          <div v-if="duration > 0" class="flex items-center gap-1 text-gray-500 ml-2 flex-shrink-0">
            <span class="text-gray-500 mr-1">Duration:</span>
            <span>{{ formatTime(duration) }}</span>
          </div>
        </div>
        <a-button @click="resetVideo" size="small">Reset</a-button>
      </div>
      
      <!-- Video preview -->
      <div class="relative rounded-md overflow-hidden bg-black">
        <div ref="videoContainer" class="relative">
          <video
            ref="videoRef"
            :src="videoUrl"
            class="max-w-full max-h-[400px] object-contain mx-auto"
            @loadedmetadata="handleVideoLoaded"
            @timeupdate="handleTimeUpdate"
            @click="togglePlayPause"
          ></video>
        </div>
        
        <!-- Custom video controls -->
        <div class="mt-2 p-2 bg-gray-100 rounded-md">
          <div class="flex items-center space-x-2">
            <!-- Play/Pause button -->
            <button 
              @click="togglePlayPause" 
              class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded"
            >
              {{ isPlaying ? '⏸️ Pause' : '▶️ Play' }}
            </button>
            
            <!-- Progress bar -->
            <div class="flex-grow h-2 bg-gray-300 rounded-full overflow-hidden cursor-pointer" @click="seekVideo">
              <div 
                class="h-full bg-blue-500" 
                :style="{ width: `${videoProgress}%` }"
              ></div>
            </div>
            
            <!-- Time display -->
            <div class="text-xs text-gray-600">
              {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Cutting options -->
      <div class="space-y-4 p-4 border border-gray-200 rounded-md">
        <h3 class="text-lg font-medium">Cutting Options</h3>
        
        <!-- Cutting mode selection -->
        <div class="space-y-1">
          <label class="text-sm font-medium">Cutting Mode</label>
          <a-radio-group v-model:value="cuttingMode" class="w-full">
            <a-radio value="equal">Equal Parts</a-radio>
            <a-radio value="custom">Custom Segments</a-radio>
          </a-radio-group>
        </div>
        
        <!-- Equal parts mode -->
        <div v-if="cuttingMode === 'equal'" class="space-y-2">
          <div class="space-y-1">
            <label class="text-sm font-medium">Number of Parts</label>
            <a-input-number 
              v-model:value="numberOfParts" 
              :min="2" 
              :max="20"
              class="w-full"
            />
          </div>
          
          <div class="text-sm text-gray-500">
            Each part will be approximately {{ formatTime(partDuration) }} long
          </div>
        </div>
        
        <!-- Custom segments mode -->
        <div v-if="cuttingMode === 'custom'" class="space-y-2">
          <div class="flex justify-between items-center">
            <label class="text-sm font-medium">Custom Segments</label>
            <a-button size="small" @click="addSegment" :disabled="!duration">Add Segment</a-button>
          </div>
          
          <div v-if="segments.length === 0" class="text-sm text-gray-500 italic">
            No segments added. Click "Add Segment" to create a new segment.
          </div>
          
          <div v-for="(segment, index) in segments" :key="index" class="flex items-center space-x-2 p-2 bg-gray-50 rounded-md">
            <div class="flex-grow grid grid-cols-2 gap-2">
              <div>
                <label class="text-xs text-gray-500">Start Time</label>
                <a-input-number 
                  v-model:value="segment.start" 
                  :min="0" 
                  :max="duration - 1"
                  :precision="0"
                  class="w-full"
                  @change="validateSegments"
                />
              </div>
              <div>
                <label class="text-xs text-gray-500">Duration (seconds)</label>
                <a-input-number 
                  v-model:value="segment.duration" 
                  :min="1" 
                  :max="duration - segment.start"
                  :precision="0"
                  class="w-full"
                  @change="validateSegments"
                />
              </div>
            </div>
            <a-button 
              danger 
              size="small" 
              @click="removeSegment(index)"
              class="mt-4"
            >
              Remove
            </a-button>
          </div>
        </div>
        
        <!-- Output settings -->
        <div class="space-y-1 mt-4">
          <label class="text-sm font-medium">Output Settings</label>
          
          <div class="grid grid-cols-2 gap-2">
            <div>
              <label class="text-xs text-gray-500">Video Codec</label>
              <a-select v-model:value="videoCodec" class="w-full">
                <a-select-option value="h264_nvenc">NVIDIA GPU (h264_nvenc)</a-select-option>
                <a-select-option value="h264">CPU (h264)</a-select-option>
              </a-select>
            </div>
            <div>
              <label class="text-xs text-gray-500">Audio Bitrate (kbps)</label>
              <a-select v-model:value="audioBitrate" class="w-full">
                <a-select-option value="128">128 kbps</a-select-option>
                <a-select-option value="192">192 kbps</a-select-option>
                <a-select-option value="256">256 kbps</a-select-option>
              </a-select>
            </div>
          </div>
          
          <div class="mt-2">
            <label class="text-xs text-gray-500">Output Directory</label>
            <div class="flex space-x-2">
              <a-input v-model:value="outputDir" placeholder="Leave empty to use same directory as input" />
              <a-button @click="selectOutputDir">Browse</a-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Process buttons -->
      <div class="flex justify-end space-x-2">
        <a-button 
          v-if="isProcessing && currentProcessId"
          danger
          @click="stopProcessing"
        >
          Stop Processing
        </a-button>
        <a-button 
          type="primary" 
          @click="processVideo" 
          :loading="isProcessing"
          :disabled="!videoFile || isProcessing || !isValidForProcessing"
        >
          {{ isProcessing ? 'Processing...' : 'Cut Video' }}
        </a-button>
      </div>
      
      <!-- Processing status -->
      <div v-if="isProcessing" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div class="flex items-center">
          <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
          <p class="text-blue-700">{{ processingStatus }}</p>
        </div>
        <div v-if="processingProgress > 0" class="mt-2">
          <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: `${processingProgress}%` }"></div>
          </div>
          <p class="text-xs text-blue-700 mt-1">{{ Math.round(processingProgress) }}% complete</p>
        </div>
      </div>
      
      <!-- Result -->
      <div v-if="outputFiles.length > 0" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
        <div class="flex justify-between items-center mb-2">
          <p class="text-green-700 font-medium">Video cutting complete!</p>
          <a-button size="small" @click="openOutputFolder">Open Output Folder</a-button>
        </div>
        <div class="max-h-40 overflow-y-auto">
          <div v-for="(file, index) in outputFiles" :key="index" class="flex justify-between items-center py-1 border-b border-green-100">
            <span class="text-sm text-gray-600">{{ file.name }}</span>
            <a-button size="small" @click="openFile(file.path)">Open</a-button>
          </div>
        </div>
      </div>
      
      <!-- Error -->
      <div v-if="error" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <p class="text-red-700">Error: {{ error }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount } from 'vue';
import { message } from 'ant-design-vue';
import DragDropUpload from './DragDropUpload.vue';

// Reactive state
const videoFile = ref(null);
const videoUrl = ref(null);
const videoRef = ref(null);
const videoContainer = ref(null);
const isProcessing = ref(false);
const processingStatus = ref('');
const processingProgress = ref(0);
const currentProcessId = ref(null);
const error = ref(null);
const outputFiles = ref([]);
const outputDir = ref('');

// Video player state
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const videoProgress = ref(0);

// Cutting options
const cuttingMode = ref('equal');
const numberOfParts = ref(2);
const segments = ref([]);
const videoCodec = ref('h264_nvenc');
const audioBitrate = ref('192');

// Computed properties
const partDuration = computed(() => {
  if (!duration.value || numberOfParts.value < 2) return 0;
  return Math.floor(duration.value / numberOfParts.value);
});

const isValidForProcessing = computed(() => {
  if (!videoFile.value || !duration.value) return false;
  
  if (cuttingMode.value === 'equal') {
    return numberOfParts.value >= 2;
  } else {
    return segments.value.length > 0;
  }
});

// Handle video selection
const handleVideoSelected = (files) => {
  if (files && files.length > 0) {
    handleVideoFile(files[0]);
  }
};

// Process video file
const handleVideoFile = (file) => {
  // Check file type
  if (!file.type.startsWith('video/')) {
    message.error('Invalid file type. Please select a video file.');
    return;
  }

  // Revoke old URL to avoid memory leaks
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }

  videoFile.value = file;
  videoUrl.value = `file://${file.path}`;
  
  // Reset result and error
  outputFiles.value = [];
  error.value = null;
  segments.value = [];
};

// Handle video loaded metadata
const handleVideoLoaded = () => {
  if (videoRef.value) {
    duration.value = videoRef.value.duration;
    
    // Reset video player state
    currentTime.value = 0;
    isPlaying.value = false;
    videoProgress.value = 0;
    
    // Add initial segment if in custom mode
    if (cuttingMode.value === 'custom' && segments.value.length === 0) {
      addSegment();
    }
  }
};

// Handle video time update
const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime;
    videoProgress.value = (currentTime.value / duration.value) * 100;
  }
};

// Toggle play/pause
const togglePlayPause = () => {
  if (!videoRef.value) return;
  
  if (isPlaying.value) {
    videoRef.value.pause();
  } else {
    videoRef.value.play();
  }
  
  isPlaying.value = !isPlaying.value;
};

// Seek video to position
const seekVideo = (event) => {
  if (!videoRef.value) return;
  
  const rect = event.target.getBoundingClientRect();
  const pos = (event.clientX - rect.left) / rect.width;
  videoRef.value.currentTime = pos * duration.value;
};

// Format time (seconds to HH:MM:SS format)
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00:00';
  
  const hrs = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// Add a new segment
const addSegment = () => {
  if (!duration.value) return;
  
  // Calculate a good starting point for the new segment
  let startTime = 0;
  if (segments.value.length > 0) {
    const lastSegment = segments.value[segments.value.length - 1];
    startTime = Math.min(lastSegment.start + lastSegment.duration, Math.floor(duration.value - 10));
  }
  
  // Calculate a reasonable duration (10 seconds or remaining time)
  const remainingTime = Math.max(1, Math.floor(duration.value - startTime));
  const segmentDuration = Math.min(10, remainingTime);
  
  segments.value.push({
    start: startTime,
    duration: segmentDuration
  });
};

// Remove a segment
const removeSegment = (index) => {
  segments.value.splice(index, 1);
};

// Validate segments to ensure they don't exceed video duration
const validateSegments = () => {
  segments.value.forEach(segment => {
    // Ensure start time is within video duration
    segment.start = Math.max(0, Math.min(segment.start, Math.floor(duration.value - 1)));
    
    // Ensure duration doesn't exceed video length
    segment.duration = Math.max(1, Math.min(segment.duration, Math.floor(duration.value - segment.start)));
  });
};

// Select output directory
const selectOutputDir = async () => {
  try {
    const result = await window.electronAPI.openFileDialog({
      properties: ['openDirectory']
    });
    
    if (!result.canceled && result.filePaths && result.filePaths.length > 0) {
      outputDir.value = result.filePaths[0];
    }
  } catch (err) {
    console.error('Error selecting output directory:', err);
    message.error('Error selecting output directory');
  }
};

// Process video
const processVideo = async () => {
  if (!videoFile.value || !isValidForProcessing.value) return;
  
  isProcessing.value = true;
  error.value = null;
  outputFiles.value = [];
  currentProcessId.value = null;
  processingProgress.value = 0;
  
  try {
    processingStatus.value = 'Preparing to cut video...';
    
    // Prepare segments based on cutting mode
    let segmentsToProcess = [];
    
    if (cuttingMode.value === 'equal') {
      // Create equal segments
      for (let i = 0; i < numberOfParts.value; i++) {
        segmentsToProcess.push({
          start: i * partDuration.value,
          duration: partDuration.value,
          partNumber: i
        });
      }
    } else {
      // Use custom segments
      segmentsToProcess = segments.value.map((segment, index) => ({
        ...segment,
        partNumber: index
      }));
    }
    
    // Process each segment
    const results = [];
    for (let i = 0; i < segmentsToProcess.length; i++) {
      const segment = segmentsToProcess[i];
      processingStatus.value = `Cutting part ${i + 1} of ${segmentsToProcess.length}...`;
      processingProgress.value = (i / segmentsToProcess.length) * 100;
      
      const result = await window.electronAPI.cutVideoSegment({
        inputPath: videoFile.value.path,
        outputDir: outputDir.value || videoFile.value.path,
        startTime: formatTime(segment.start),
        duration: formatTime(segment.duration),
        partNumber: segment.partNumber,
        videoCodec: videoCodec.value,
        audioBitrate: audioBitrate.value
      });
      
      if (result.success) {
        results.push(result);
        currentProcessId.value = result.processId;
        
        // Wait for processing to complete
        await checkProcessStatus();
      } else {
        throw new Error(result.error || `Failed to process part ${i + 1}`);
      }
    }
    
    // All segments processed successfully
    processingProgress.value = 100;
    processingStatus.value = 'All parts processed successfully!';
    
    // Collect output files
    outputFiles.value = results.map(result => ({
      name: result.outputPath.split(/[\/\\]/).pop(),
      path: result.outputPath
    }));
    
    message.success('Video cutting completed successfully');
  } catch (err) {
    error.value = err.message || 'Unknown error occurred';
    message.error('Error processing video: ' + error.value);
  } finally {
    isProcessing.value = false;
    processingStatus.value = '';
    currentProcessId.value = null;
  }
};

// Stop processing
const stopProcessing = async () => {
  if (!currentProcessId.value) return;
  
  try {
    const result = await window.electronAPI.stopProcess(currentProcessId.value);
    
    if (result.success) {
      message.success('Processing stopped');
      isProcessing.value = false;
      currentProcessId.value = null;
    } else {
      message.error('Failed to stop processing: ' + (result.error || 'Unknown error'));
    }
  } catch (err) {
    message.error('Error stopping process: ' + (err.message || 'Unknown error'));
  }
};

// Check process status periodically
const checkProcessStatus = async () => {
  if (!currentProcessId.value || !isProcessing.value) return;
  
  try {
    const result = await window.electronAPI.getActiveProcesses();
    
    if (result.success) {
      // Check if our process is still running
      const isRunning = result.processes.some(p => p.processId === currentProcessId.value);
      
      if (!isRunning) {
        // Process completed
        return;
      } else {
        // Check again after 1 second
        await new Promise(resolve => setTimeout(resolve, 1000));
        return await checkProcessStatus();
      }
    }
  } catch (err) {
    console.error('Error checking process status:', err);
    // Continue checking even if there's an error
    await new Promise(resolve => setTimeout(resolve, 1000));
    return await checkProcessStatus();
  }
};

// Open output folder
const openOutputFolder = async () => {
  if (outputFiles.value.length > 0) {
    const folderPath = outputFiles.value[0].path.split(/[\/\\]/).slice(0, -1).join('/');
    await window.electronAPI.openFolder(folderPath);
  }
};

// Open a file
const openFile = async (filePath) => {
  if (filePath) {
    await window.electronAPI.openFile(filePath);
  }
};

// Reset video
const resetVideo = () => {
  // Stop video playback if playing
  if (videoRef.value && isPlaying.value) {
    videoRef.value.pause();
    isPlaying.value = false;
  }
  
  // Revoke object URL
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }
  
  // Reset video state
  videoFile.value = null;
  videoUrl.value = null;
  currentTime.value = 0;
  duration.value = 0;
  videoProgress.value = 0;
  outputFiles.value = [];
  error.value = null;
  segments.value = [];
  
  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopProcessing();
  } else {
    currentProcessId.value = null;
  }
};

// Clean up on component unmount
onBeforeUnmount(() => {
  // Stop video playback if playing
  if (videoRef.value && isPlaying.value) {
    videoRef.value.pause();
  }
  
  // Revoke object URL
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value);
  }
  
  // If there's an active process, stop it
  if (currentProcessId.value && isProcessing.value) {
    stopProcessing();
  }
});
</script>
