<template>
  <div class="video-processor">
    <h2>Video Processor</h2>
    <div>
      <label>Input Video Path:</label>
      <input v-model="videoPath" type="text" placeholder="Enter video file path" />
    </div>
    <div>
      <label>Subtitle Style:</label>
      <select v-model="subtitleStyle">
        <option value="white">White Background, Black Text</option>
        <option value="yellow">Yellow Background, Black Text</option>
      </select>
    </div>
    <div>
      <label>Optional Text:</label>
      <input v-model="customText" type="text" placeholder="Enter text overlay" />
    </div>
    <div>
      <label>Logo Path:</label>
      <input v-model="logoPath" type="text" placeholder="Enter logo file path" />
    </div>
    <div>
      <label>Thumbnail Path:</label>
      <input v-model="thumbnailPath" type="text" placeholder="Enter thumbnail file path" />
    </div>
    <button @click="processVideo">Process Video</button>
    <div v-if="status">{{ status }}</div>
  </div>
</template>

<script>
import { ref } from 'vue';
// import { processSrtAndVideo } from '@/lib/ffmpegProcessor';


const srtArray =[
    {
        "index": 1,
        "id": 1,
        "text": "有的孩子是孩子",
        "startTime": 0,
        "endTime": 1.28,
        "start": "00:00:00,000",
        "end": "00:00:01,280",
        "translatedText": "Có những đứa trẻ đúng là trẻ con",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\co-nhung-dua-tre-dung-la1747904739230.mp3",
        "duration": 0,
        "isGenerated": true,
        "audioUrl1": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\co-nhung-dua-tre-dung-la1747904739230.mp3",
        "audioDuration1": 1992,
        "isGenerated1": true,
        "isVoice": 1,
        "trim": 0.25
    },
    {
        "index": 2,
        "id": 2,
        "text": "有的孩子是商品",
        "startTime": 1.28,
        "endTime": 3.96,
        "start": "00:00:01,280",
        "end": "00:00:03,960",
        "translatedText": "Có những đứa trẻ bị coi như món hàng",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\co-nhung-dua-tre-bi-coi-1747904741080.mp3",
        "duration": 0,
        "isGenerated": true,
        "audioUrl1": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\co-nhung-dua-tre-bi-coi-1747904741080.mp3",
        "audioDuration1": 2280,
        "isGenerated1": true,
        "isVoice": 1,
        "trim": 0.25
    },
    {
        "index": 3,
        "id": 3,
        "text": "我叫鄭秉瑞",
        "startTime": 3.96,
        "endTime": 5.12,
        "start": "00:00:03,960",
        "end": "00:00:05,120",
        "translatedText": "Tôi tên là Trịnh Bỉnh Thụy  ",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\toi-ten-la-trinh-binh-th1747904742461.mp3",
        "duration": 0,
        "isGenerated": true,
        "audioUrl1": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\toi-ten-la-trinh-binh-th1747904742461.mp3",
        "audioDuration1": 1392,
        "isGenerated1": true,
        "isVoice": 1,
        "trim": 0.25
    },
    {
        "index": 4,
        "id": 4,
        "text": "是個熱衷於做慈善的商人",
        "startTime": 5.12,
        "endTime": 7.16,
        "start": "00:00:05,120",
        "end": "00:00:07,160",
        "translatedText": "Là một thương nhân đam mê hoạt động từ thiện  ",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\la-mot-thuong-nhan-dam-m1747904743932.mp3",
        "duration": 0,
        "isGenerated": true,
        "audioUrl3": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\la-mot-thuong-nhan-dam-m1747904743932.mp3",
        "audioDuration3": 2105,
        "isGenerated3": true,
        "isVoice": 3,
        "trim": 0.25
    },
    {
        "index": 5,
        "id": 5,
        "text": "都說好人有好夢",
        "startTime": 7.16,
        "endTime": 8.6,
        "start": "00:00:07,160",
        "end": "00:00:08,600",
        "translatedText": "Người ta vẫn bảo người tốt sẽ có giấc mơ đẹp  ",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\nguoi-ta-van-bao-nguoi-t1747904745825.mp3",
        "duration": 0,
        "isGenerated": true,
        "audioUrl2": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\nguoi-ta-van-bao-nguoi-t1747904745825.mp3",
        "audioDuration2": 2075,
        "isGenerated2": true,
        "isVoice": 2,
        "trim": 0.25
    },
    {
        "index": 6,
        "id": 6,
        "text": "可我怎麼也想不到",
        "startTime": 8.6,
        "endTime": 9.84,
        "start": "00:00:08,600",
        "end": "00:00:09,840",
        "translatedText": "Nhưng tôi không thể ngờ được  ",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\nhung-toi-khong-the-ngo-1747904747172.mp3",
        "duration": 0,
        "isGenerated": true,
        "isVoice": 2,
        "audioUrl2": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\nhung-toi-khong-the-ngo-1747904747172.mp3",
        "audioDuration2": 1320,
        "isGenerated2": true,
        "trim": 0.25
    },
    {
        "index": 7,
        "id": 7,
        "text": "女兒婷婷竟會在自己家裡被人綁架",
        "startTime": 9.84,
        "endTime": 12.44,
        "start": "00:00:09,840",
        "end": "00:00:12,440",
        "translatedText": "Con gái Đình Đình lại bị bắt cóc ngay trong chính nhà mình  ",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\con-gai-dinh-dinh-lai-bi1747904747949.mp3",
        "duration": 0,
        "isGenerated": true,
        "isVoice": 2,
        "audioUrl2": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\con-gai-dinh-dinh-lai-bi1747904747949.mp3",
        "audioDuration2": 2736,
        "isGenerated2": true,
        "trim": 0.25
    },
    {
        "index": 8,
        "id": 8,
        "text": "又在幾個小時前",
        "startTime": 12.44,
        "endTime": 13.8,
        "start": "00:00:12,440",
        "end": "00:00:13,800",
        "translatedText": "Chỉ vài giờ trước đó  ",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\chi-vai-gio-truoc-do1747904749272.mp3",
        "duration": 0,
        "isGenerated": true,
        "isVoice": 2,
        "audioUrl2": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\chi-vai-gio-truoc-do1747904749272.mp3",
        "audioDuration2": 1320,
        "isGenerated2": true,
        "trim": 0.25
    },
    {
        "index": 9,
        "id": 9,
        "text": "當地的佛教洗禮儀式",
        "startTime": 13.8,
        "endTime": 15.48,
        "start": "00:00:13,800",
        "end": "00:00:15,480",
        "translatedText": "Nghi thức rửa tội Phật giáo địa phương  ",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\nghi-thuc-rua-toi-phat-g1747904750005.mp3",
        "duration": 0,
        "isGenerated": true,
        "isVoice": 2,
        "audioUrl2": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\nghi-thuc-rua-toi-phat-g1747904750005.mp3",
        "audioDuration2": 2016,
        "isGenerated2": true,
        "trim": 0.25
    },
    {
        "index": 10,
        "id": 10,
        "text": "在我的別墅如期舉行",
        "startTime": 15.48,
        "endTime": 17.24,
        "start": "00:00:15,480",
        "end": "00:00:17,240",
        "translatedText": "Đã diễn ra đúng kế hoạch tại biệt thự của tôi",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\da-dien-ra-dung-ke-hoach1747904751075.mp3",
        "duration": 0,
        "isGenerated": true,
        "isVoice": 2,
        "audioUrl2": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\da-dien-ra-dung-ke-hoach1747904751075.mp3",
        "audioDuration2": 2160,
        "isGenerated2": true,
        "trim": 0.25
    },
    {
        "index": 11,
        "id": 11,
        "text": "婷婷的老師慧萍",
        "startTime": 17.24,
        "endTime": 18.68,
        "start": "00:00:17,240",
        "end": "00:00:18,680",
        "translatedText": "Cô giáo Tuệ Bình của Đình Đình  ",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\co-giao-tue-binh-cua-din1747904752093.mp3",
        "duration": 0,
        "isGenerated": true,
        "isVoice": 2,
        "audioUrl2": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\co-giao-tue-binh-cua-din1747904752093.mp3",
        "audioDuration2": 1565,
        "isGenerated2": true,
        "trim": 0.25
    },
    {
        "index": 12,
        "id": 12,
        "text": "帶著孩子們在院子裡玩起了捉迷藏",
        "startTime": 18.68,
        "endTime": 21.08,
        "start": "00:00:18,680",
        "end": "00:00:21,080",
        "translatedText": "Dẫn các em nhỏ ra sân chơi trốn tìm",
        "status": "translated",
        "selectedSpeaker": "tts.other.BV075_streaming",
        "speechRate": 0,
        "audioUrl": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\dan-cac-em-nho-ra-san-ch1747904753762.mp3",
        "duration": 0,
        "isGenerated": true,
        "isVoice": 2,
        "audioUrl2": "file://C:\\Users\\<USER>\\Downloads\\phim3_audio\\dan-cac-em-nho-ra-san-ch1747904753762.mp3",
        "audioDuration2": 2145,
        "isGenerated2": true,
        "trim": 0.25
    }
]





export default {
  name: 'VideoProcessor',
//   props: {
//     srtArray: {
//       type: Array,
//       required: true,
//       validator: (arr) => arr.every(item => 
//         item.index && item.id && item.text && item.startTime && item.endTime && 
//         item.start && item.end && item.translatedText && item.audioUrl
//       ),
//     },
//   },
  setup(props) {
    const videoPath = ref('');
    const subtitleStyle = ref('white');
    const customText = ref('');
    const logoPath = ref('');
    const thumbnailPath = ref('');
    const status = ref('');

    const processVideo = async () => {
      try {
        status.value = 'Processing...';
        const outputDir = 'F:\\Footage\\0.-any\\batcoc'
        const outputVideo = `${outputDir}\\output.mp4`

        await electronAPI.processSrtAndVideo({
          srtArray: srtArray,
          videoPath: videoPath.value,
          outputDir,
          outputVideo,
          subtitleStyle: subtitleStyle.value,
          customText: customText.value,
          logoPath: logoPath.value,
          thumbnailPath: thumbnailPath.value,
        });

        status.value = `Video processed successfully: ${outputVideo}`;
      } catch (error) {
        status.value = `Error: ${error.message}`;
      }
    };

    return {
      videoPath,
      subtitleStyle,
      customText,
      logoPath,
      thumbnailPath,
      status,
      processVideo,
    };
  },
};
</script>

<style scoped>
.video-processor {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}
input, select, button {
  margin: 10px 0;
  padding: 8px;
  width: 100%;
}
button {
  background-color: #007bff;
  color: white;
  border: none;
  cursor: pointer;
}
button:hover {
  background-color: #0056b3;
}
</style>