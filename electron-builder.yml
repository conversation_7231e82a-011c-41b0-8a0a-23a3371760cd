appId: com.ttsapp.desktop
productName: TTS App
artifactName: ${productName}_${version}-${os}-${arch}.${ext}
asar: true

protocols:
  name: ttsapp
  schemes:
    - ttsapp

files:
  - package.json
  # - electron/resources/**/*
  - dist/**/*
  - from: ./jsc/
    to: electron/
  # - from: electron-build/preload.js
  #   to: electron/preload.js
# mac:
#   category: public.app-category.developer-tools
#   icon: electron/resources/icons/mac/icon.icns
#   entitlements: electron/resources/mac/entitlements.mac.plist
#   entitlementsInherit: electron/resources/mac/entitlements.mac.plist
#   hardenedRuntime: true
#   gatekeeperAssess: false
#   target: dmg

win:
  target: nsis
  # icon: electron/resources/icons/win/icon.ico

nsis:
  oneClick: true
  allowToChangeInstallationDirectory: false

# linux:
#   target: tar.gz
#   icon: electron/resources/icons/png/512x512.png

directories:
  output: dist_electron

publish:
  provider: github
  releaseType: release
  vPrefixedTagName: false
