import { ChatOpenAI } from '@langchain/openai';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { state } from './state';
// import { translateText } from "./translationService";

// let terminologyDictionary = {};
let aiTransMode = 'deepseek';
let modelName = 'deepseek-chat';
let configParams = {
  apiKey: '',
  basePath: 'https://api.deepseek.com/v1',
};

// Function to set API key and model
export function configureTranslateService(apiKey, model = 'deepseek-chat') {
  configParams.apiKey = apiKey;
  modelName = model;

  // Set the translation mode based on the model
  if (model.includes('deepseek')) {
    aiTransMode = 'deepseek';
  } else if (model.includes('gemini')) {
    aiTransMode = 'gemini';
  } else if (model.includes('gpt')) {
    aiTransMode = 'openai';
  }

  console.log(`Configured translate service with model: ${model}, mode: ${aiTransMode}`);
}

let genAI = null;
let geminiModel = null;

// Initialize Gemini model when needed
function initGeminiModel(apiKey) {
  if (!genAI && apiKey) {
    try {
      genAI = new GoogleGenerativeAI(apiKey);
      geminiModel = genAI.getGenerativeModel({ model: 'gemini-pro' });
      console.log('Gemini model initialized');
    } catch (error) {
      console.error('Error initializing Gemini model:', error);
    }
  }
  return geminiModel;
}

// Hàm trích xuất tên riêng và thuật ngữ quan trọng từ văn bản
async function extractImportantTerms(text) {
  const model = new ChatOpenAI({
    modelName: modelName,
    temperature: 0,
    openAIApiKey: configParams.apiKey,
    streaming: false,
    cache: true,
    configuration: {
      baseURL: configParams.basePath,
    },
    maxTokens: -1, // Không giới hạn token
    maxRetries: 3,
    // Tắt việc tính toán token
    tokenCountingFunction: () => ({ totalCount: 0, countsByFunction: {} }),
  });

  console.log('Đang phân tích tên nhân vật và thuật ngữ quan trọng...');

  const response = await model.invoke([
    new SystemMessage(`Bạn là chuyên gia phân tích văn bản. Hãy trích xuất tất cả tên nhân vật, địa điểm, và thuật ngữ quan trọng từ văn bản.
Lưu ý: Trích xuất đúng họ tên nhân vật địa điểm từ văn bản.
Hãy trả về kết quả dưới dạng JSON với định dạng sau:
{
  "characters": ["tên_nhân_vật_1", "tên_nhân_vật_2", ...],
  "locations": ["địa_điểm_1", "địa_điểm_2", ...],
  "terms": ["thuật_ngữ_1", "thuật_ngữ_2", ...]
}

Chỉ trả về JSON, không thêm bất kỳ giải thích nào.`),
    new HumanMessage(text.substring(0, 10000)), // Phân tích một phần đầu của văn bản
  ]);

  try {
    // Trích xuất phần JSON từ phản hồi
    const jsonMatch = response.content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const extractedTerms = JSON.parse(jsonMatch[0]);
      return extractedTerms;
    }
  } catch (error) {
    console.error('Lỗi khi phân tích JSON:', error.message);
  }

  return { characters: [], locations: [], terms: [] };
}

// Hàm xây dựng từ điển thuật ngữ
export async function buildTerminologyDictionary(text, sourceLang) {
  const terms = await extractImportantTerms(text);

  // Tạo danh sách tất cả các thuật ngữ cần dịch
  const allTerms = [...(terms.characters || []), ...(terms.locations || []), ...(terms.terms || [])].filter(
    (term) => term && term.trim().length > 0,
  );

  if (allTerms.length === 0) {
    console.log('Không tìm thấy thuật ngữ quan trọng.');
    return {};
  }

  console.log(`Đã tìm thấy ${allTerms.length} thuật ngữ quan trọng.`);

  // Nếu có quá nhiều thuật ngữ, chỉ lấy những thuật ngữ quan trọng nhất
  const termsToTranslate = allTerms.length > 50 ? allTerms.slice(0, 50) : allTerms;

  console.log('Đang xây dựng từ điển thuật ngữ...');
  const dictionary = {};

  // Dịch các thuật ngữ
  const model = new ChatOpenAI({
    modelName: modelName,
    temperature: 0.3,
    openAIApiKey: configParams.apiKey,
    streaming: false,
    cache: true,
    configuration: {
      baseURL: configParams.basePath,
    },
    maxTokens: -1, // Không giới hạn token
    maxRetries: 3,
    // Tắt việc tính toán token
    tokenCountingFunction: () => ({ totalCount: 0, countsByFunction: {} }),
  });

  const response = await model.invoke([
    new SystemMessage(`Bạn là chuyên gia dịch thuật. Hãy dịch các thuật ngữ sau từ ${sourceLang} sang tiếng Việt.

Hãy dịch sang tiếng Việt một cách thuần Việt và tự nhiên nhất có thể, và dịch đúng họ tên nhân vật địa điểm nếu có.

Trả về kết quả dưới dạng JSON với định dạng sau:
{
  "term1": "bản dịch1",
  "term2": "bản dịch2",
  ...
}

Chỉ trả về JSON, không thêm bất kỳ giải thích nào.`),
    new HumanMessage(termsToTranslate.join('\n')),
  ]);

  try {
    // Trích xuất phần JSON từ phản hồi
    const jsonMatch = response.content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const translatedTerms = JSON.parse(jsonMatch[0]);

      // Hiển thị từ điển thuật ngữ
      console.log('Từ điển thuật ngữ:');
      for (const [term, translation] of Object.entries(translatedTerms)) {
        console.log(`  ${term} => ${translation}`);
        dictionary[term] = translation;
      }

      return dictionary;
    }
  } catch (error) {
    console.error('Lỗi khi phân tích JSON từ điển:', error.message);
  }

  return {};
}

function createTranslationPrompt2(sourceLang = 'auto', targetLang = 'Vietnamese', dictionary = {}) {
  let dictionaryText = '';

  // Nếu có từ điển thuật ngữ, thêm vào prompt
  if (Object.keys(dictionary).length > 0) {
    dictionaryText = 'Từ điển thuật ngữ (hãy sử dụng nhất quán):\n';
    for (const [term, translation] of Object.entries(dictionary)) {
      dictionaryText += `- ${term}: ${translation}\n`;
    }
    dictionaryText += '\n';
  }

  return `Bạn là một dịch giả chuyên nghiệp. Nhiệm vụ của bạn là dịch văn bản từ ${
    sourceLang === 'auto' ? 'ngôn ngữ được phát hiện' : sourceLang
  } sang ${targetLang}.

${dictionaryText}Hướng dẫn:
1. Dịch toàn bộ văn bản được cung cấp, không bỏ sót nội dung.
2. Giữ nguyên số thứ tự. Chỉ trả về phần dịch, không bao gồm câu gốc.
3. KHÔNG giữ lại bất kỳ từ nào bằng ngôn ngữ gốc, trừ tên riêng.
4. KHÔNG thêm bất kỳ ghi chú hay giải thích nào.
5. Sử dụng từ điển thuật ngữ ở trên để đảm bảo tính nhất quán khi dịch tên nhân vật và thuật ngữ chuyên ngành.
6. Nếu phát hiện nhiều câu giống hệt nhau, hãy đảm bảo dịch chúng một cách nhất quán.

Hãy dịch văn bản sau đây thành ${targetLang} một cách tự nhiên và chính xác:`;
}

// Hàm hiển thị thanh tiến trình
function showProgressBar(current, total, barLength = 30) {
  const progress = Math.round((current / total) * barLength);
  const progressBar = '█'.repeat(progress) + '░'.repeat(barLength - progress);
  const percentage = Math.round((current / total) * 100);
  //   process.stdout.write(
  //     `\r[${progressBar}] ${percentage}% (${current}/${total} chunks)`
  //   );
}

// Hàm dịch văn bản với streaming
async function translateTextDeepseek(textList, existingTranslations = {}, startIndex = 0, terminologyDictionary = {}, targetLang = 'Vietnamese') {
  const sourceLang = 'Chinese';

  // Lọc ra các dòng cần dịch (chưa có trong existingTranslations)
  const textsToTranslate = [];
  const indices = [];

  textList.forEach((text, i) => {
    const globalIndex = startIndex + i + 1;
    if (!existingTranslations[globalIndex]) {
      textsToTranslate.push(text);
      indices.push(globalIndex - 1); // Chỉ số trong mảng bắt đầu từ 0
    }
  });

  if (textsToTranslate.length === 0) {
    console.log('Tất cả các dòng trong nhóm này đã được dịch trước đó.');
    return textList.map((_, i) => {
      const globalIndex = startIndex + i + 1;
      return existingTranslations[globalIndex] || '';
    });
  }

  // Tạo prompt dịch thuật với từ điển thuật ngữ
  const translationPrompt = createTranslationPrompt2(
    sourceLang,
    targetLang,
    terminologyDictionary
  );

  const model = new ChatOpenAI({
    modelName: modelName,
    temperature: 0.3, // Giảm temperature để đảm bảo tính nhất quán
    openAIApiKey: configParams.apiKey,
    streaming: true,
    cache: true,
    configuration: {
      baseURL: configParams.basePath,
    },
  });

  const formattedText = textsToTranslate.map((text, i) => `${indices[i] + 1}. ${text}`).join('\n');

  const prompt = `${translationPrompt}
${formattedText}`;

  console.log('Kết quả stream:');
  try {
    let chunkTranslation = '';
    // Sử dụng stream thay vì invoke
    const stream = await model.stream([new SystemMessage(translationPrompt), new HumanMessage(prompt)]);

    // Xử lý stream
    for await (const chunk of stream) {
      const token = chunk.content;
      state.contentStream += token;
      chunkTranslation += token;
    }

    const translatedLines = chunkTranslation.trim().split('\n');

    // Xử lý kết quả trả về
    const translatedDict = { ...existingTranslations }; // Bắt đầu với các dịch đã có
    for (const line of translatedLines) {
      const match = line.match(/^(\d+)\.\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
      }
    }
    // Lưu tiến độ dịch
    // F.saveTranslationProgress(outputSrt, translatedDict);

    // Kiểm tra các dòng trùng lặp
    const duplicates = checkDuplicateLines(textList);
    if (duplicates.length > 0) {
      console.log(`\nPhát hiện ${duplicates.length} nhóm dòng trùng lặp trong nhóm này`);

      // Xử lý các dòng trùng lặp để đảm bảo dịch nhất quán
      duplicates.forEach((dup) => {
        // Lấy chỉ số toàn cục
        const globalIndices = dup.indices.map((idx) => startIndex + idx + 1);

        // Lấy bản dịch đầu tiên làm chuẩn
        const firstGlobalIndex = globalIndices[0];
        if (translatedDict[firstGlobalIndex]) {
          const standardTranslation = translatedDict[firstGlobalIndex];

          // Áp dụng cho tất cả các dòng trùng lặp
          globalIndices.forEach((idx) => {
            translatedDict[idx] = standardTranslation;
          });

          console.log(`Đã chuẩn hóa dịch cho ${globalIndices.length} dòng trùng lặp`);
        }
      });

      // Cập nhật lại tiến độ sau khi xử lý trùng lặp
      // F.saveTranslationProgress(outputSrt, translatedDict);
    }

    // Khớp nội dung dịch với thứ tự ban đầu
    const translatedTexts = textList.map((text, i) => {
      const globalIndex = startIndex + i + 1;
      return translatedDict[globalIndex] || text;
    });

    console.log('\n'); // Xuống dòng sau khi stream kết thúc
    return translatedTexts;
  } catch (error) {
    console.error(`\nLỗi khi dịch: ${error}`);
    console.log('Đang thử lại sau 3 giây...');
    await new Promise((resolve) => setTimeout(resolve, 3000));
    return await translateTextDeepseek(textList, existingTranslations, startIndex, terminologyDictionary);
  }
}

function checkDuplicateLines(textList) {
  const duplicates = [];
  const uniqueTexts = new Set();
  const duplicateIndices = {};

  textList.forEach((text, index) => {
    if (uniqueTexts.has(text)) {
      if (!duplicateIndices[text]) {
        duplicateIndices[text] = [];
      }
      duplicateIndices[text].push(index);
    } else {
      uniqueTexts.add(text);
    }
  });

  // Tìm các dòng trùng lặp liên tiếp
  for (const text in duplicateIndices) {
    const indices = duplicateIndices[text];
    if (indices.length > 0) {
      let consecutiveGroup = [indices[0]];

      for (let i = 1; i < indices.length; i++) {
        if (indices[i] === indices[i - 1] + 1) {
          consecutiveGroup.push(indices[i]);
        } else {
          if (consecutiveGroup.length > 2) {
            duplicates.push({
              text,
              indices: [...consecutiveGroup],
            });
          }
          consecutiveGroup = [indices[i]];
        }
      }

      if (consecutiveGroup.length > 2) {
        duplicates.push({
          text,
          indices: [...consecutiveGroup],
        });
      }
    }
  }

  return duplicates;
}

async function batchTranslateText(textList, existingTranslations = {}, terminologyDictionary = {}, targetLang = 'Vietnamese') {
  /** Gửi toàn bộ danh sách phụ đề lên API dịch một lần, giữ nguyên số thứ tự */
  if (!textList || textList.length === 0) {
    return textList;
  }

  // Kiểm tra các dòng trùng lặp
  const duplicates = checkDuplicateLines(textList);
  if (duplicates.length > 0) {
    console.log(`Phát hiện ${duplicates.length} nhóm dòng trùng lặp`);
  }

  // Lọc ra các dòng cần dịch (chưa có trong existingTranslations)
  const textsToTranslate = [];
  const indices = [];

  textList.forEach((text, i) => {
    const key = `${i + 1}`;
    if (!existingTranslations[key]) {
      textsToTranslate.push(text);
      indices.push(i);
    }
  });

  if (textsToTranslate.length === 0) {
    console.log('Tất cả các dòng đã được dịch trước đó.');
    return textList.map((_, i) => existingTranslations[`${i + 1}`] || '');
  }

  console.log(`Cần dịch ${textsToTranslate.length} dòng mới`);

  // Tạo prompt dịch thuật với từ điển thuật ngữ
  const translationPrompt = createTranslationPrompt2('Chinese', targetLang, terminologyDictionary);
  const formattedText = textsToTranslate.map((text, i) => `${indices[i] + 1}. ${text}`).join('\n');

  const prompt = `${translationPrompt}
${formattedText}`;

  try {
    // Initialize Gemini model if needed
    const model = initGeminiModel(configParams.apiKey);
    if (!model) {
      throw new Error('Failed to initialize Gemini model');
    }

    const result = await model.generateContent(prompt);
    const response = result.response;
    const translatedText = response.text();
    const translatedLines = translatedText.trim().split('\n');

    // Xử lý kết quả trả về
    const translatedDict = { ...existingTranslations }; // Bắt đầu với các dịch đã có
    for (const line of translatedLines) {
      const match = line.match(/^(\d+)\.\s*(.*)/);
      if (match) {
        const index = parseInt(match[1]);
        const translatedText = match[2];
        translatedDict[index] = translatedText;
      }
    }

    // Lưu tiến độ dịch
    // F.saveTranslationProgress(outputSrt, translatedDict);

    // Khớp nội dung dịch với thứ tự ban đầu
    const translatedTexts = textList.map((text, i) => translatedDict[i + 1] || text);

    // Xử lý các dòng trùng lặp - đảm bảo dịch nhất quán
    duplicates.forEach((dup) => {
      // Lấy bản dịch đầu tiên làm chuẩn
      const firstIndex = dup.indices[0];
      const standardTranslation = translatedTexts[firstIndex];

      // Áp dụng cho tất cả các dòng trùng lặp
      dup.indices.forEach((idx) => {
        translatedTexts[idx] = standardTranslation;
        translatedDict[idx + 1] = standardTranslation;
      });
    });

    // Cập nhật lại tiến độ sau khi xử lý trùng lặp
    // F.saveTranslationProgress(outputSrt, translatedDict);

    // F.l('translatedTexts', translatedTexts);
    return translatedTexts;
  } catch (e) {
    console.error(`Lỗi dịch: ${e}`);
    return textList; // Nếu lỗi thì giữ nguyên bản gốc
  }
}
export async function translateSrtService({ subs, batchSize = 50, onProgress = null, terminologyDictionary, targetLanguage = 'Vietnamese' }) {
  /** Dịch file SRT bằng cách gửi toàn bộ nội dung lên API một lần */
  try {
    console.log(`Đã phân tích ${subs.length} phụ đề từ file SRT.`);

    // Đọc tiến độ dịch đã lưu (nếu có)
    const existingTranslations = {};

    // Chia nhỏ danh sách phụ đề thành các nhóm để tránh quá tải API
    const batches = [];

    for (let i = 0; i < subs.length; i += batchSize) {
      batches.push(subs.slice(i, i + batchSize));
    }

    // Cập nhật tiến độ - bắt đầu xây dựng từ điển thuật ngữ
    if (onProgress) onProgress(0, batches.length);

    console.log('Bắt đầu xây dựng từ điển thuật ngữ...');

    // Lấy mẫu văn bản để xây dựng từ điển thuật ngữ
    // const Texts = subs.slice(0, 100).map(sub => sub.text);
    // const sampleText = Texts.join('\n\n');

    // terminologyDictionary = await buildTerminologyDictionary(
    //   sampleText,
    //   'Chinese'
    // );

    console.log(`Chia thành ${batches.length} nhóm để dịch.`);

    // Cập nhật tiến độ - đã xây dựng từ điển thuật ngữ
    if (onProgress) onProgress(1, batches.length);

    // Dịch từng nhóm phụ đề
    let allTranslatedTexts = [];
    for (let i = 0; i < batches.length; i++) {
      console.log(`Đang dịch nhóm ${i + 1}/${batches.length}...`);
      // showProgressBar(i+1, batches.length);

      // Cập nhật tiến độ
      if (onProgress) onProgress(i + 1, batches.length);

      // Lấy các văn bản cần dịch trong nhóm này
      const batchTexts = batches[i].map((sub) => sub.text);

      // Tính chỉ số bắt đầu của nhóm này trong danh sách đầy đủ
      const startIndex = i * batchSize;

      // Lọc các bản dịch đã có cho nhóm này
      const batchExistingTranslations = {};
      for (let j = 0; j < batchTexts.length; j++) {
        const globalIndex = startIndex + j + 1;
        if (existingTranslations[globalIndex]) {
          batchExistingTranslations[globalIndex] = existingTranslations[globalIndex];
        }
      }
      console.log(batchTexts);
      console.log(existingTranslations);
      console.log(startIndex);
      console.log(terminologyDictionary);

      // Dịch nhóm văn bản
      const translatedBatch =
        aiTransMode === 'deepseek'
          ? await translateTextDeepseek(batchTexts, existingTranslations, startIndex, terminologyDictionary, targetLanguage)
          : await batchTranslateText(batchTexts, existingTranslations, terminologyDictionary, targetLanguage);

      allTranslatedTexts = [...allTranslatedTexts, ...translatedBatch];
    }

    // Cập nhật tiến độ - hoàn thành
    if (onProgress) onProgress(batches.length, batches.length);

    console.log('Dịch hoàn tất!');
    // showProgressBar(batches.length, batches.length);

    return allTranslatedTexts;
  } catch (e) {
    console.error(`Error in translateSrt: ${e}`);
    throw e;
  }
}
