@echo off
chcp 65001
set input=%*

REM Kích hoạt virtual environment
@REM cd /d C:\Users\<USER>\Desktop\Note\MyTool\translate
call venv\Scripts\activate.bat

echo Input: %input%

REM Xử lý từng file truy<PERSON>n vào
@REM for %%f in (%*) do (
@REM     set "video=%%f"
@REM     setlocal enabledelayedexpansion
@REM     set "srt=%%~dpf%%~nf-ocr.srt"
@REM     echo Running: python main.py --video "%%f" --output "!srt!" --crop 0,0.85,1,1
@REM     python main.py --video "%%f" --output "!srt!" --frame_rate 3 --lang zh
@REM 	REM --crop 0,0.85,1,1
@REM     endlocal
@REM )

echo Running: python scripts\main.py --video %1 --output %2 --frame_rate %3 --lang %4 --crop %5

python scripts\main.py --video %1 --output %2 --frame_rate %3 --lang %4 --crop %5




@REM pause
