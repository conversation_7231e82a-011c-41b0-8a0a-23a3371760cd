// Hàm tạo header ASS với tùy chọn màu sắc
const createASSHeader = (options = {}) => {
  // Cấu hình mặc định
  const config = {
    fontSize: options.fontSize || 48,
    fontName: options.fontName || 'Arial',
    
    // <PERSON><PERSON><PERSON> chữ (Primary Color) - format: &H00BBGGRR (BGR hex)
    textColor: options.textColor || '&H00FFFFFF', // Trắng
    
    // <PERSON><PERSON><PERSON> nền (Back Color) - format: &HAARRGGBB 
    // AA = alpha (transparency), 00 = opaque, FF = transparent
    backgroundColor: options.backgroundColor || '&H80000000', // Đen semi-transparent
    
    // Màu viền (Outline Color)
    outlineColor: options.outlineColor || '&H00000000', // Đen
    
    // <PERSON>ích thước viền và shadow
    outlineSize: options.outlineSize || 2,
    shadowSize: options.shadowSize || 2,
    
    // Alignment: 1=left, 2=center, 3=right, 5=top-left, 6=top-center, etc.
    alignment: options.alignment || 2,
    
    // Margins
    marginLeft: options.marginLeft || 20,
    marginRight: options.marginRight || 20,
    marginVertical: options.marginVertical || 30,
    
    // Bold, Italic
    bold: options.bold ? -1 : 0,
    italic: options.italic ? -1 : 0,
    
    // Background box style
    // BorderStyle: 1 = outline and shadow, 3 = background box, 4 = background box with outline
    borderStyle: options.borderStyle || 3, // 3 = background box
    
    // Padding cho background box (chỉ hoạt động với BorderStyle 3 hoặc 4)
    // Sử dụng MarginV để tạo padding vertical
    // Horizontal padding được tạo bằng cách thêm spaces trong text
  };
const resolution = options.resolution || {};
  const header = `[Script Info]
Title: Untitled
ScriptType: v4.00+
PlayResX: ${resolution.width || 1920}
PlayResY: ${resolution.height || 1080}
WrapStyle: 0
Collisions: Reverse

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${config.fontName},${config.fontSize},${config.textColor},&H000000FF,${config.outlineColor},${config.backgroundColor},${config.bold},${config.italic},0,0,100,100,0,0,${config.borderStyle},${config.outlineSize},${config.shadowSize},${config.alignment},${config.marginLeft},${config.marginRight},${config.marginVertical},1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text`.trim();

  return header;
};
// Hàm sinh file ASS từ danh sách phụ đề
const generateASSSubtitle = (srtArray, options = {}) => {
  const header = createASSHeader(options);

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = (seconds % 60).toFixed(2).padStart(5, '0');
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs}`;
  };

  let events = '';
  srtArray.forEach((srt, index) => {
    const startTime = formatTime(srt.adjustedStartTime || srt.startTime);
    const endTime = formatTime(srt.adjustedEndTime || srt.endTime);
    const text = srt.translatedText || srt.text || '';

    const paddedText = options.addPadding ? ` ${text} ` : text;
    const assOptions = options.assOptions || {};
    const resolution = options.resolution || {};
    const tag = buildASSPositionTag(assOptions, resolution);

    const line = `Dialogue: 0,${startTime},${endTime},Default,,0,0,0,,{${tag}}${paddedText}\n`;
    events += line;
  });

  return header + '\n\n' + events;
};
function buildASSPositionTag({ pos, rotation, align }, resolution) {
  if (!pos) return '';
  const x = Math.round((pos.x / 1000) * resolution.width);
  const y = Math.round((pos.y / 1000) * resolution.height);
  const an = align || 5;
  const rot = rotation !== undefined ? `\\frz(${rotation})` : '';
  return `\\an(${an})\\pos(${x},${y})${rot}`;
}
const cssToASSColor = (cssHex, alpha = '00') => {
  const hex = cssHex.replace('#', '');
  const r = hex.substring(0, 2);
  const g = hex.substring(2, 4);
  const b = hex.substring(4, 6);
  return `&H${alpha}${b}${g}${r}`.toUpperCase();
};
function convertPercentToPixel(percent, total) {
  return Math.round((percent / 100) * total);
}


function clampAtTempo(value) {
  return Math.max(0.5, Math.min(100, parseFloat(value)));
}


module.exports = {
  createASSHeader,
  generateASSSubtitle,
  cssToASSColor,
  clampAtTempo
};
