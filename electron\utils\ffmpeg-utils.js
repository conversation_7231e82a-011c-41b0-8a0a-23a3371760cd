// ffmpeg-utils.js - Advanced FFmpeg utilities
const fs = require('fs');
const path = require('path');
const { exec, spawn } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class AdvancedFFmpegUtils {
  constructor() {
    this.progressCallback = null;
  }

  // Set progress callback
  setProgressCallback(callback) {
    this.progressCallback = callback;
  }

  // Get video information
  async getVideoInfo(videoPath) {
    try {
      const { stdout } = await execAsync(
        `ffprobe -v quiet -print_format json -show_format -show_streams "${videoPath}"`
      );
      
      const info = JSON.parse(stdout);
      const videoStream = info.streams.find(s => s.codec_type === 'video');
      const audioStream = info.streams.find(s => s.codec_type === 'audio');
      
      return {
        duration: parseFloat(info.format.duration),
        width: videoStream?.width || 0,
        height: videoStream?.height || 0,
        fps: eval(videoStream?.r_frame_rate) || 30,
        bitrate: parseInt(info.format.bit_rate) || 0,
        hasAudio: !!audioStream,
        videoCodec: videoStream?.codec_name || 'unknown',
        audioCodec: audioStream?.codec_name || 'unknown'
      };
    } catch (error) {
      console.error('Error getting video info:', error);
      return null;
    }
  }

  // Enhanced video cutting with precision
  async cutVideoSegmentPrecise(videoPath, startTime, endTime, outputPath, options = {}) {
    const {
      copyMode = false,  // false = re-encode for precision, true = copy for speed
      videoCodec = 'libx264',
      audioCodec = 'aac',
      crf = 18,
      preset = 'medium'
    } = options;

    let command;
    
    if (copyMode) {
      // Fast copy mode (less precise)
      command = `ffmpeg -ss ${startTime} -i "${videoPath}" -t ${endTime - startTime} -c copy "${outputPath}" -avoid_negative_ts make_zero -y`;
    } else {
      // Re-encode mode (more precise)
      command = `ffmpeg -i "${videoPath}" -ss ${startTime} -t ${endTime - startTime} -c:v ${videoCodec} -crf ${crf} -preset ${preset} -c:a ${audioCodec} "${outputPath}" -y`;
    }

    try {
      await execAsync(command);
      return true;
    } catch (error) {
      console.error('Error cutting video segment:', error);
      return false;
    }
  }

  // Advanced video speed adjustment
  async adjustVideoSpeed(inputPath, outputPath, speedFactor, options = {}) {
    const {
      maintainPitch = true,
      videoCodec = 'libx264',
      audioCodec = 'aac',
      crf = 18
    } = options;

    // Calculate video and audio filters
    const videoFilter = `setpts=PTS/${speedFactor}`;
    const audioFilter = maintainPitch 
      ? `atempo=${Math.min(Math.max(speedFactor, 0.5), 2.0)}` 
      : `asetrate=44100*${speedFactor},aresample=44100`;

    const command = `ffmpeg -i "${inputPath}" -filter_complex "[0:v]${videoFilter}[v];[0:a]${audioFilter}[a]" -map "[v]" -map "[a]" -c:v ${videoCodec} -crf ${crf} -c:a ${audioCodec} "${outputPath}" -y`;

    try {
      await execAsync(command);
      return true;
    } catch (error) {
      console.error('Error adjusting video speed:', error);
      return false;
    }
  }

  // Create complex video filter with multiple effects
  createComplexFilter(effects = {}) {
    const {
      logo = null,
      logoPosition = 'top-right',
      logoOpacity = 1.0,
      logoScale = 1.0,
      text = null,
      textPosition = 'bottom-center',
      textFont = 'Arial',
      textSize = 24,
      textColor = 'white',
      textBorderColor = 'black',
      textBorderWidth = 2,
      brightness = 0,
      contrast = 1,
      saturation = 1,
      blur = 0,
      sharpen = 0
    } = effects;

    let filterParts = [];
    let inputLabel = '[0:v]';

    // Color adjustments
    if (brightness !== 0 || contrast !== 1 || saturation !== 1) {
      filterParts.push(`${inputLabel}eq=brightness=${brightness}:contrast=${contrast}:saturation=${saturation}[color]`);
      inputLabel = '[color]';
    }

    // Blur effect
    if (blur > 0) {
      filterParts.push(`${inputLabel}boxblur=${blur}[blur]`);
      inputLabel = '[blur]';
    }

    // Sharpen effect
    if (sharpen > 0) {
      filterParts.push(`${inputLabel}unsharp=5:5:${sharpen}:5:5:0[sharp]`);
      inputLabel = '[sharp]';
    }

    // Logo overlay
    if (logo && fs.existsSync(logo)) {
      const positions = {
        'top-left': '10:10',
        'top-right': 'W-w-10:10',
        'top-center': '(W-w)/2:10',
        'bottom-left': '10:H-h-10',
        'bottom-right': 'W-w-10:H-h-10',
        'bottom-center': '(W-w)/2:H-h-10',
        'center': '(W-w)/2:(H-h)/2'
      };

      const logoFilter = `movie=${logo}:loop=0,setpts=N/(FRAME_RATE*TB),scale=iw*${logoScale}:ih*${logoScale},format=rgba,colorchannelmixer=aa=${logoOpacity}[logo]`;
      const overlayFilter = `${inputLabel}[logo]overlay=${positions[logoPosition] || positions['top-right']}[logo_out]`;
      
      filterParts.push(logoFilter);
      filterParts.push(overlayFilter);
      inputLabel = '[logo_out]';
    }

    // Text overlay
    if (text) {
      const positions = {
        'top-left': 'x=10:y=30',
        'top-right': 'x=w-text_w-10:y=30',
        'top-center': 'x=(w-text_w)/2:y=30',
        'bottom-left': 'x=10:y=h-text_h-30',
        'bottom-right': 'x=w-text_w-10:y=h-text_h-30',
        'bottom-center': 'x=(w-text_w)/2:y=h-text_h-30',
        'center': 'x=(w-text_w)/2:y=(h-text_h)/2'
      };

      const textFilter = `${inputLabel}drawtext=text='${text}':fontfile='${textFont}':fontsize=${textSize}:fontcolor=${textColor}:bordercolor=${textBorderColor}:borderw=${textBorderWidth}:${positions[textPosition] || positions['bottom-center']}[text_out]`;
      filterParts.push(textFilter);
      inputLabel = '[text_out]';
    }

    return {
      filter: filterParts.join(';'),
      outputLabel: inputLabel
    };
  }

  // Enhanced audio synchronization
  async synchronizeAudio(videoPath, audioPath, outputPath, options = {}) {
    const {
      audioDelay = 0,
      videoDelay = 0,
      crossfadeDuration = 0.1,
      audioVolume = 1.0,
      videoAudioVolume = 0.0,
      sampleRate = 44100,
      videoBitrate = '1M',
      audioBitrate = '128k'
    } = options;

    let audioFilters = [];
    let videoFilters = [];

    // Audio delay
    if (audioDelay !== 0) {
      audioFilters.push(`adelay=${Math.abs(audioDelay * 1000)}|${Math.abs(audioDelay * 1000)}`);
    }

    // Video delay
    if (videoDelay !== 0) {
      videoFilters.push(`setpts=PTS+${videoDelay}/TB`);
    }

    // Audio volume adjustment
    if (audioVolume !== 1.0) {
      audioFilters.push(`volume=${audioVolume}`);
    }

    // Build filter complex
    let filterComplex = '';
    if (videoFilters.length > 0) {
      filterComplex += `[0:v]${videoFilters.join(',')}[v];`;
    }
    
    if (audioFilters.length > 0) {
      filterComplex += `[1:a]${audioFilters.join(',')}[a];`;
    }

    // Mix audio if needed
    if (videoAudioVolume > 0) {
      filterComplex += `[0:a]volume=${videoAudioVolume}[va];[va][a]amix=inputs=2:duration=longest[audio_out]`;
    }

    let command = `ffmpeg -i "${videoPath}" -i "${audioPath}"`;
    
    if (filterComplex) {
      command += ` -filter_complex "${filterComplex}"`;
      command += videoFilters.length > 0 ? ' -map "[v]"' : ' -map 0:v';
      command += audioFilters.length > 0 || videoAudioVolume > 0 ? 
        (videoAudioVolume > 0 ? ' -map "[audio_out]"' : ' -map "[a]"') : ' -map 1:a';
    } else {
      command += ' -map 0:v -map 1:a';
    }

    command += ` -c:v libx264 -b:v ${videoBitrate} -c:a aac -b:a ${audioBitrate} -ar ${sampleRate} -shortest "${outputPath}" -y`;

    try {
      await execAsync(command);
      return true;
    } catch (error) {
      console.error('Error synchronizing audio:', error);
      return false;
    }
  }

  // Batch processing with progress tracking
  async processVideoSegments(segments, processingFunction, options = {}) {
    const results = [];
    const total = segments.length;
    
    for (let i = 0; i < total; i++) {
      const segment = segments[i];
      
      try {
        // Update progress
        if (this.progressCallback) {
          this.progressCallback({
            current: i + 1,
            total: total,
            percentage: Math.round(((i + 1) / total) * 100),
            currentItem: segment,
            stage: 'processing'
          });
        }

        // Process segment
        const result = await processingFunction(segment, i, options);
        results.push({
          ...segment,
          result,
          success: true
        });

      } catch (error) {
        console.error(`Error processing segment ${i}:`, error);
        results.push({
          ...segment,
          error: error.message,
          success: false
        });
      }
    }

    return results;
  }

  // Quality analysis
  async analyzeVideoQuality(videoPath) {
    try {
      // Get basic info
      const info = await this.getVideoInfo(videoPath);
      
      // Analyze video quality metrics
      const { stdout } = await execAsync(
        `ffprobe -f lavfi -i "movie=${videoPath},signalstats" -show_entries frame=pkt_pts_time:frame_tags=lavfi.signalstats.YAVG,lavfi.signalstats.UAVG,lavfi.signalstats.VAVG -of csv=p=0 -t 10`
      );

      const lines = stdout.trim().split('\n');
      const metrics = lines.map(line => {
        const parts = line.split(',');
        return {
          time: parseFloat(parts[0]),
          brightness: parseFloat(parts[1]),
          chromaU: parseFloat(parts[2]),
          chromaV: parseFloat(parts[3])
        };
      });

      const avgBrightness = metrics.reduce((sum, m) => sum + m.brightness, 0) / metrics.length;
      
      return {
        ...info,
        quality: {
          avgBrightness,
          samples: metrics.length,
          recommendedCRF: this.recommendCRF(info, avgBrightness)
        }
      };
    } catch (error) {
      console.error('Error analyzing video quality:', error);
      return null;
    }
  }

  // Recommend CRF based on content
  recommendCRF(videoInfo, avgBrightness) {
    let baseCRF = 23;
    
    // Adjust based on resolution
    if (videoInfo.width >= 1920) baseCRF = 20; // 1080p+
    else if (videoInfo.width >= 1280) baseCRF = 22; // 720p
    else baseCRF = 25; // Lower resolution
    
    // Adjust based on brightness (darker content needs lower CRF)
    if (avgBrightness < 100) baseCRF -= 2;
    else if (avgBrightness > 180) baseCRF += 1;
    
    return Math.max(15, Math.min(30, baseCRF));
  }

  // Create fade transitions between segments
  async createFadeTransition(segment1Path, segment2Path, outputPath, fadeType = 'cross', duration = 0.5) {
    const fadeTypes = {
      'cross': `[0:v][1:v]xfade=transition=fade:duration=${duration}:offset=0[v];[0:a][1:a]acrossfade=d=${duration}[a]`,
      'dissolve': `[0:v][1:v]xfade=transition=dissolve:duration=${duration}:offset=0[v];[0:a][1:a]acrossfade=d=${duration}[a]`,
      'wipe': `[0:v][1:v]xfade=transition=wipeleft:duration=${duration}:offset=0[v];[0:a][1:a]acrossfade=d=${duration}[a]`,
      'slide': `[0:v][1:v]xfade=transition=slideleft:duration=${duration}:offset=0[v];[0:a][1:a]acrossfade=d=${duration}[a]`
    };

    const filter = fadeTypes[fadeType] || fadeTypes['cross'];
    
    const command = `ffmpeg -i "${segment1Path}" -i "${segment2Path}" -filter_complex "${filter}" -map "[v]" -map "[a]" -c:v libx264 -crf 18 -c:a aac "${outputPath}" -y`;

    try {
      await execAsync(command);
      return true;
    } catch (error) {
      console.error('Error creating fade transition:', error);
      return false;
    }
  }
}

module.exports = AdvancedFFmpegUtils;