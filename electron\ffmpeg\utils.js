const ffmpeg = require("fluent-ffmpeg");

// <PERSON>àm lấy độ dài video
function getVideoDuration(inputFile) {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(inputFile, (err, metadata) => {
      if (err) {
        reject(err);
      } else {
        resolve(metadata.format.duration); // độ dài video (giây)
      }
    });
  });
}

// Hàm để cắt một đoạn video
function cutVideoSegment(inputFile, outputFile, startTime, duration) {
  return new Promise((resolve, reject) => {
    ffmpeg(inputFile)
      .setStartTime(startTime)
      .setDuration(duration)
      .output(outputFile)
      .videoCodec("copy")
      .noAudio()
      .on("end", () => {
        console.log(`Conversion done for ${outputFile}`);
        resolve();
      })
      .on("error", (err) => {
        console.log(`Error for ${outputFile}:`, err);
        reject(err);
      })
      .run();
  });
}