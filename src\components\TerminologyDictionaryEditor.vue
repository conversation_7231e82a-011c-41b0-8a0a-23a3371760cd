<template>
  <div>
    <!-- Button to open the modal -->
    <a-button @click="showModal" :disabled="!hasDictionary">
      {{ buttonText }}
    </a-button>

    <!-- Modal for editing terminology dictionary -->
    <a-modal
      v-model:open="visible"
      :title="t('terminologyDictionary.title')"
      width="800px"
      @ok="handleOk"
      @cancel="handleCancel"
      :okText="t('common.save')"
      :cancelText="t('common.cancel')"
    >
      <div class="space-y-4">
        <div class="flex justify-between items-center">
          <div class="text-lg font-medium">
            {{ t('terminologyDictionary.entries', { count: Object.keys(dictionary).length }) }}
          </div>
          <div class="space-x-2">
            <a-button @click="addNewEntry" type="primary" size="small">
              {{ t('terminologyDictionary.addNew') }}
            </a-button>
            <a-button @click="clearDictionary" danger size="small">
              {{ t('terminologyDictionary.clear') }}
            </a-button>
          </div>
        </div>

        <!-- Search input -->
        <a-input
          v-model:value="searchTerm"
          :placeholder="t('terminologyDictionary.search')"
          class="mb-4"
          allow-clear
        />

        <!-- Dictionary entries table -->
        <div class="max-h-[400px] overflow-y-auto border border-gray-700 rounded-md">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-800 sticky top-0">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ t('terminologyDictionary.originalTerm') }}
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ t('terminologyDictionary.translation') }}
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ t('common.actions') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-slate-900 divide-y divide-gray-200">
              <tr v-if="filteredEntries.length === 0">
                <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                  {{ t('terminologyDictionary.noEntries') }}
                </td>
              </tr>
              <tr v-for="(entry, index) in filteredEntries" :key="index" class="hover:bg-slate-800">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div v-if="editingIndex === index">
                    <a-input v-model:value="editingTerm" />
                  </div>
                  <div v-else>{{ entry.term }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div v-if="editingIndex === index">
                    <a-input v-model:value="editingTranslation" />
                  </div>
                  <div v-else>{{ entry.translation }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium ">
                  <div v-if="editingIndex === index" class="space-x-2 gap-1">
                    <a-button size="small" type="primary" @click="saveEdit">
                      {{ t('common.save') }}
                    </a-button>
                    <a-button size="small" @click="cancelEdit">
                      {{ t('common.cancel') }}
                    </a-button>
                  </div>
                  <div v-else class="space-x-2 gap-1">
                    <a-button size="small" @click="editEntry(entry.term, entry.translation, index)">
                      {{ t('common.edit') }}
                    </a-button>
                    <a-button size="small" danger @click="removeEntry(entry.term)">
                      {{ t('common.delete') }}
                    </a-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { useTTSStore } from '@/stores/ttsStore';
import { useI18n } from '@/i18n/i18n';

const props = defineProps({
  buttonText: {
    type: String,
    default: 'Edit Terminology Dictionary'
  }
});

const emit = defineEmits(['update']);

const ttsStore = useTTSStore();
const { t } = useI18n();

// State variables
const visible = ref(false);
const dictionary = ref({});
const searchTerm = ref('');
const editingIndex = ref(-1);
const editingTerm = ref('');
const editingTranslation = ref('');
const originalTerm = ref('');

// Computed properties
const hasDictionary = computed(() => {
  return Object.keys(ttsStore.terminologyDictionary || {}).length > 0;
});

const filteredEntries = computed(() => {
  const entries = Object.entries(dictionary.value).map(([term, translation]) => ({
    term,
    translation
  }));
  
  if (!searchTerm.value) return entries;
  
  const search = searchTerm.value.toLowerCase();
  return entries.filter(
    entry => 
      entry.term.toLowerCase().includes(search) || 
      entry.translation.toLowerCase().includes(search)
  );
});

// Methods
const showModal = () => {
  dictionary.value = { ...ttsStore.terminologyDictionary };
  visible.value = true;
};

const handleOk = () => {
  ttsStore.terminologyDictionary = { ...dictionary.value };
  message.success(t('terminologyDictionary.saved'));
  emit('update', dictionary.value);
  visible.value = false;
};

const handleCancel = () => {
  visible.value = false;
};

const addNewEntry = () => {
  editingIndex.value = filteredEntries.value.length;
  editingTerm.value = '';
  editingTranslation.value = '';
  originalTerm.value = '';
};

const editEntry = (term, translation, index) => {
  editingIndex.value = index;
  editingTerm.value = term;
  editingTranslation.value = translation;
  originalTerm.value = term;
};

const saveEdit = () => {
  if (!editingTerm.value.trim()) {
    message.error(t('terminologyDictionary.termRequired'));
    return;
  }
  
  // If we're editing an existing entry, remove the old key
  if (originalTerm.value && originalTerm.value !== editingTerm.value) {
    const { [originalTerm.value]: _, ...rest } = dictionary.value;
    dictionary.value = rest;
  }
  
  // Add or update the entry
  dictionary.value = {
    ...dictionary.value,
    [editingTerm.value]: editingTranslation.value
  };
  
  // Reset editing state
  editingIndex.value = -1;
  editingTerm.value = '';
  editingTranslation.value = '';
  originalTerm.value = '';
};

const cancelEdit = () => {
  editingIndex.value = -1;
  editingTerm.value = '';
  editingTranslation.value = '';
  originalTerm.value = '';
};

const removeEntry = (term) => {
  const { [term]: _, ...rest } = dictionary.value;
  dictionary.value = rest;
};

const clearDictionary = () => {
  dictionary.value = {};
};
</script>
