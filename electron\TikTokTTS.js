const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { execSync } = require('child_process');
const { randomInt } = require('crypto');

const ENDPOINT_DATA = [
  // { url: 'https://tiktok-tts.weilnet.workers.dev/api/generation', response: 'data' },
  { url: 'https://tiktok-tts.printmechanicalbeltpumpkingutter.workers.dev/api/generation', response: 'audio' },
  //   { url: 'https://countik.com/api/text/speech', response: 'v_data' },
  { url: 'https://translate.volcengine.com/crx/tts/v1/', response: 'audio.data' },
//   { url: 'https://gesserit.co/api/tiktok-tts', response: 'base64' },
  { url: 'https://api16-normal-v6.tiktokv.com/media/api/text/speech/invoke', response: 'data.v_str', type: 'tiktok' },
];

class TikTokTTS {
  constructor(config) {
    this.config = config;
    this.maxChars = 300; // giới hạn ký tự mỗi chunk
    this.uriBase = 'https://api16-normal-c-useast1a.tiktokv.com/media/api/text/speech/invoke/';
    this.headers = {
      'User-Agent':
        'com.zhiliaoapp.musically/2022600030 (Linux; U; Android 7.1.2; es_ES; SM-G988N; Build/NRD90M;tt-ok/3.12.13.1)',
      Cookie: `sessionid=${config.tiktok_sessionid}`,
    };
    this.session = axios.create({ headers: this.headers });
    this.volcengineHeaders = {
      'Content-Type': 'application/json',
      Authority: 'translate.volcengine.com',
      Origin: 'chrome-extension://klgfhbdadaspgppeadghjjemk',
      Cookie: 'hasUserBehavior=1',
      'user-agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36',
    };
    this.volcengineSession = axios.create({ headers: this.volcengineHeaders });
  }
  detectLang(text) {
    // Regex nhận diện tiếng Trung (ký tự CJK Unified Ideographs)
    const chineseRegex = /[\u4E00-\u9FFF]/;

    // Regex nhận diện dấu tiếng Việt
    const vietnameseRegex = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;

    if (chineseRegex.test(text)) {
      return 'zh';
    } else if (vietnameseRegex.test(text)) {
      return 'vi';
    } else {
      return 'en';
    }
  }
  randomEndpoint() {
    return ENDPOINT_DATA[randomInt(ENDPOINT_DATA.length)];
  }

  async run(text, filepath, voice, randomEndpoint = false) {
    if (!voice) throw new Error('No voice provided');

    const chunks = this._splitText(text);

    // Use either a random endpoint or iterate through all endpoints
    const endpoints = randomEndpoint ? [this.randomEndpoint()] : ENDPOINT_DATA;

    for (const entry of endpoints) {
      let endpointValid = true;
      const tempFiles = [];
      console.log('Using endpoint: ', entry);

      for (let i = 0; i < chunks.length; i++) {
        if (!endpointValid) break;
        const chunk = chunks[i];
        if (!chunk) continue;
        // or skip chunk only . or , .,!?:;\n
        if (/^[.,!?;:]+$/.test(chunk.trim())) continue;
        try {
          const volcBody = {
                  text: chunk,
                  speaker: voice,
                  language: this.detectLang(chunk),
                }
                console.log('volcBody: ', volcBody);
          const isVocengine = entry.url.includes('volcengine') && this.config?.tiktok_sessionid;
          const response =
            entry.type === 'tiktok'
              ? await this.session.post(
                  `${entry.url}/?text_speaker=${voice.replace(
                    'tts.other.',
                    '',
                  )}&req_text=${chunk}&speaker_map_type=0&aid=1233`,
                )
              : isVocengine
              ? await this.volcengineSession.post(entry.url, volcBody)
              : await axios.post(entry.url, {
                  text: chunk,
                  voice: voice.replace('tts.other.', ''),
                });
          // console.log('response: ', response.data);
          if (response.status === 200 && response.data[entry.response]) {
            const audioBase64 = response.data[entry.response];
            const buffer = Buffer.from(audioBase64, 'base64');

            // Lưu từng file tạm
            const tempFile = filepath + `_part_${i}.mp3`;
            this.saveFile(tempFile, buffer);
            tempFiles.push(tempFile);
          } else if (response.status === 200 && entry.type === 'tiktok') {
            const status_code = response.data.status_code;
            if (status_code !== 0) return this.handleStatusError(status_code);
            const audioBase64 = response.data.data.v_str;
            const buffer = Buffer.from(audioBase64, 'base64');

            // Lưu từng file tạm
            const tempFile = filepath + `_part_${i}.mp3`;
            this.saveFile(tempFile, buffer);
            tempFiles.push(tempFile);
          } else if (response.status === 200 && isVocengine) {
            const base64Audio = response.data?.audio?.data;
            if (!base64Audio) {
              throw new Error('Không nhận được audio base64 từ server.');
            }
            const buffer = Buffer.from(base64Audio, 'base64');
            // Lưu từng file tạm
            const tempFile = filepath + `_part_${i}.mp3`;
            this.saveFile(tempFile, buffer);
            tempFiles.push(tempFile);
          } else {
            endpointValid = false;
          }
        } catch (error) {
          console.error('Error:', error.message);
          endpointValid = false;
        }
      }

      if (!endpointValid) {
        // Xóa các file tạm nếu có
        for (const f of tempFiles) {
          if (fs.existsSync(f)) fs.unlinkSync(f);
        }
        continue;
      }

      if (tempFiles.length === 1) {
        // Nếu chỉ 1 chunk thì rename thẳng
        fs.renameSync(tempFiles[0], filepath);
      } else {
        // Tạo file danh sách để ffmpeg nối
        const listFile = filepath + '_list.txt';
        const fileContent = tempFiles.map((f) => `file '${path.resolve(f)}'`).join('\n');
        fs.writeFileSync(listFile, fileContent);

        // Nối file với ffmpeg
        try {
          execSync(
            `ffmpeg -y -f concat -safe 0 -i "${listFile}" -ar 44100 -ac 2 -acodec libmp3lame -q:a 2 "${filepath}"`,
          );
        } catch (e) {
          console.error('FFmpeg concat error:', e);
          // Xóa tạm rồi return lỗi
          tempFiles.forEach((f) => fs.unlinkSync(f));
          fs.unlinkSync(listFile);
          throw e;
        }

        // Xóa file tạm và file danh sách
        tempFiles.forEach((f) => fs.unlinkSync(f));
        fs.unlinkSync(listFile);
      }

      console.log(`File '${filepath}' has been generated successfully.`);

      break;
    }
    return filepath;
  }
  handleStatusError(status_code) {
    switch (status_code) {
      case 1:
        throw new Error(
          `Your TikTok session id might be invalid or expired. Try getting a new one. status_code: ${status_code}`,
        );
      case 2:
        throw new Error(`The provided text is too long. status_code: ${status_code}`);
      case 4:
        throw new Error(`Invalid speaker, please check the list of valid speaker values. status_code: ${status_code}`);
      case 5:
        throw new Error(`No session id found. status_code: ${status_code}`);
    }
  }
  _splitText(text) {
    // Tách đoạn dựa trên dấu câu và khoảng trắng, giữ giới hạn 300 ký tự
    const separatedChunks = [...text.matchAll(/.*?[.,!?:;\n]|.+/g)].map((m) => m[0]);
    const finalChunks = [];

    for (let chunk of separatedChunks) {
      if (chunk.length > this.maxChars) {
        // tách nhỏ theo khoảng trắng nếu quá dài
        const words = chunk.split(' ');
        let temp = '';
        for (const word of words) {
          if ((temp + word).length > this.maxChars) {
            if (temp) finalChunks.push(temp.trim());
            temp = word + ' ';
          } else {
            temp += word + ' ';
          }
        }
        if (temp) finalChunks.push(temp.trim());
      } else {
        finalChunks.push(chunk.trim());
      }
    }

    return finalChunks;
  }
  slugify(text) {
    return text
      .normalize('NFD') // Tách dấu khỏi ký tự
      .replace(/[\u0300-\u036f]/g, '') // Xóa các dấu
      .replace(/đ/g, 'd') // Chuyển đ -> d
      .replace(/Đ/g, 'd')
      .toLowerCase()
      .replace(/\s+/g, '-') // Thay khoảng trắng bằng -
      .replace(/[^\w\-]+/g, '') // Xóa ký tự không phải chữ/số/gạch ngang
      .replace(/\-\-+/g, '-') // Gộp nhiều dấu - liên tiếp
      .replace(/^-+|-+$/g, ''); // Xóa - ở đầu và cuối
  }
  saveFile(filepath, buffer) {
    fs.writeFileSync(filepath, buffer);
  }
}

module.exports = TikTokTTS;

// const tts = new TikTokTTS({ tiktok_sessionid: 'f99b95b348771c84efde83a0d457cc4d' });
// const filePath = path.join(__dirname,'..','_test-tts', 'cache', 'tts', 'output2.mp3');
// Example 1: Using the default behavior (try all endpoints in sequence)
// tts.run(`Tôi chỉ là một con yêu rắn nhỏ bé, không mang trong mình linh khí hay sức mạnh huyền bí nào.`, filePath, false, true);

// Example 2: Using a random endpoint (set the 5th parameter to true)
// tts.run(`Tôi chỉ là một con yêu rắn nhỏ bé, không mang trong mình linh khí hay sức mạnh huyền bí nào.`, filePath, 'BV074_streaming', true, true);
// tts.run(`Tôi chỉ là một con yêu rắn nhỏ bé, không mang trong mình linh khí hay sức mạnh huyền bí nào.`, filePath, 'tts.other.BV074_streaming', true, true);
