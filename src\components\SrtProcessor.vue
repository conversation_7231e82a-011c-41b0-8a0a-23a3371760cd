<script setup>
import { ref, computed } from "vue";
import { useTTSStore } from "../stores/ttsStore";

const ttsStore = useTTSStore();
// const selectedSpeaker = ref(ttsStore.selectedSpeaker);
const srtFile = ref(null);
const isProcessing = computed(() => ttsStore.isLoading);
const processingStatus = computed(() => ttsStore.processingStatus);
const srtAudios = computed(() => ttsStore.srtAudios);
const speakers = computed(() => ttsStore.speakers);
const error = computed(() => ttsStore.error);
const outputFileName = ref("combined-audio.mp3");
const isCombining = ref(false);

async function handleFileUpload(e) {
  const file = e.target.files[0];
  if (file) {
    console.log(e.target.files);

    srtFile.value = file;
  }
}

async function processSRT() {
  if (!srtFile.value) {
    return;
  }

  ttsStore.setSelectedSpeaker(ttsStore.selectedSpeaker);
  await ttsStore.processSRTFile(srtFile.value);
}

function formatTime(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 1000);
  return `${mins}:${secs.toString().padStart(2, "0")}.${ms.toString().padStart(3, "0")}`;
}

async function combineAudios() {
  if (srtAudios.value.length === 0) return;

  isCombining.value = true;

  try {
    // This would be handled by the Electron backend
    // For now, we'll just simulate it
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // In a real implementation, we would call the Electron API
    // to combine the audio files using FFmpeg

    isCombining.value = false;

    // Show success message
    // In a real implementation, we would provide a download link
    // to the combined audio file
  } catch (error) {
    console.error("Error combining audio files:", error);
    isCombining.value = false;
  }
}

function onClick() {
  // () => document.getElementById('srt-file').click()
  const input = document.getElementById("srt-file");
  input.click();
}
</script>

<template>
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
      SRT File Processing
    </h2>

    <div class="space-y-4">
      <div>
        <label
          for="srt-speaker"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Voice
        </label>
        <a-select
          id="srt-speaker"
          v-model:value="ttsStore.selectedSpeaker"
          class="w-full mt-1"
          placeholder="Select a voice"
        >
          <a-select-option
            v-for="(speaker, index) in speakers" :key="index"
            :value="speaker.id"
          >
            {{ speaker.name }}
          </a-select-option>
        </a-select>
      </div>

      <div>
        <label
          for="srt-file"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          SRT File
        </label>
        <div class="mt-1 flex items-center">
          <input
            type="file"
            id="srt-file"
            accept=".srt"
            @change="handleFileUpload"
            class="hidden"
          />
          <a-button @click="onClick"> Select SRT File </a-button>
          <span v-if="srtFile" class="ml-2 text-sm text-gray-500">
            {{ srtFile.name }}
          </span>
        </div>
      </div>

      <div class="flex justify-end">
        <a-button
          type="primary"
          @click="processSRT"
          :loading="isProcessing"
          :disabled="!srtFile"
        >
          Process SRT File
        </a-button>
      </div>

      <div v-if="error" class="mt-4 p-3 bg-red-100 text-red-700 rounded">
        {{ error }}
      </div>

      <div v-if="processingStatus" class="mt-4 p-3 bg-blue-100 text-blue-700 rounded">
        {{ processingStatus }}
      </div>

      <div v-if="srtAudios.length > 0" class="mt-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-md font-medium text-gray-900 dark:text-white">
            Generated Audio Segments
          </h3>

          <div class="flex items-center space-x-2">
            <a-input v-model:value="outputFileName" placeholder="Output filename" />
            <a-button type="primary" @click="combineAudios" :loading="isCombining">
              Combine All
            </a-button>
          </div>
        </div>

        <div class="space-y-4">
          <div
            v-for="(audio, index) in srtAudios"
            :key="index"
            class="bg-gray-100 dark:bg-gray-700 p-4 rounded"
          >
            <div class="flex justify-between mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                #{{ audio.index }} ({{ formatTime(audio.startTime) }} -
                {{ formatTime(audio.endTime) }})
              </span>
              <span class="text-sm text-gray-500 dark:text-gray-400">
                Duration: {{ (audio.duration / 1000).toFixed(2) }}s
              </span>
            </div>

            <p class="text-gray-800 dark:text-gray-200 mb-2">{{ audio.text }}</p>

            <audio controls class="w-full" :src="audio.audioUrl"></audio>

            <div class="mt-2 flex space-x-2">
              <a-button
                type="default"
                size="small"
                :href="audio.audioUrl"
                target="_blank"
              >
                Open in New Tab
              </a-button>
              <a-button
                type="default"
                size="small"
                @click="
                  () => {
                    const a = document.createElement('a');
                    a.href = audio.audioUrl;
                    a.download = `segment-${audio.index}.mp3`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                  }
                "
              >
                Download
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
