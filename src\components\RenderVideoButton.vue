<template>
  <div class="select-none">
    <!-- <PERSON><PERSON> -->
    <a-button
      type="primary"
      @click="showRenderModal"
      :disabled="!canRender || isProcessing"
      :loading="isProcessing"
    >
      Render Video
    </a-button>

    <!-- Render Options Modal -->
    <a-modal
      v-model:open="modalVisible"
      title="Video Render Options"
      @ok="handleRenderConfirm"
      @cancel="handleModalCancel"
      :confirm-loading="isProcessing"
      width="90%"
      :body-style="{ maxHeight: '80vh', overflow: 'auto' }"
      class="select-none"
    >
      <a-row :gutter="24">
        <!-- Video Preview Column -->
        <a-col :span="12">
          <div class="video-preview-section">
            <h3>Video Preview</h3>

            <!-- Video Player -->
            <div class="video-container">
            <VideoPlayer
                ref="videoPlayer"
                :src="ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')"
                @timeupdate="onVideoTimeUpdate"
                @loadedmetadata="onVideoLoaded"
                size="full h-full" class="preview-video"
              />
              <!-- show text animation preview -->
              <div v-if="renderOptions.showText" class="text-preview">
                <p
                  class="text-preview-content"
                  :style="{
                    fontSize: `${renderOptions.fontSize}px`,
                    color: renderOptions.textColor,
                    opacity: renderOptions.textOpacity / 100,
                  }"
                >
                  {{ renderOptions.textValue }}
                </p>
              </div>
              <!-- Drawing Canvas Overlay -->
              <canvas
                ref="drawingCanvas"
                class="drawing-canvas"
                @mousedown="handleMouseDown"
                @mousemove="handleMouseMove"
                @mouseup="handleMouseUp"
                @mouseleave="handleMouseLeave"
                @click="handleCanvasClick"
              />
            </div>

            <!-- Drawing Controls -->
            <div class="drawing-controls">
              <a-space class="flex-wrap">
                <a-button
                  :type="drawingMode === 'blur' ? 'primary' : 'default'"
                  @click="setDrawingMode('blur')"
                  size="small"
                >
                  <blur-outlined />
                  Blur Area
                </a-button>

                <a-button
                  :type="drawingMode === 'delogo' ? 'primary' : 'default'"
                  @click="setDrawingMode('delogo')"
                  size="small"
                >
                  <delete-outlined />
                  Remove Logo
                </a-button>

                <a-button
                  :type="drawingMode === 'subtitle' ? 'primary' : 'default'"
                  @click="setDrawingMode('subtitle')"
                  size="small"
                >
                  <font-colors-outlined />
                  Remove Subtitle
                </a-button>

                <a-button
                  :type="drawingMode === 'select' ? 'primary' : 'default'"
                  @click="setDrawingMode('select')"
                  size="small"
                >
                  <drag-outlined />
                  Select/Move
                </a-button>

                <a-button @click="clearDrawings" size="small">
                  <clear-outlined />
                  Clear All
                </a-button>

                <a-button @click="undoLastDrawing" size="small">
                  <undo-outlined />
                  Undo
                </a-button>

                <a-button @click="initializeDefaultBlurArea" size="small" type="dashed">
                  <font-colors-outlined />
                  Add Default Area
                </a-button>
              </a-space>

              <div v-if="selectedArea" class="selected-area-info">
                <a-divider />
                <p><strong>Selected:</strong> {{ selectedArea.type }} Area</p>
                <a-row :gutter="8">
                  <a-col :span="6">
                    <a-form-item label="X" size="small">
                      <a-input-number
                        v-model:value="selectedArea.x"
                        size="small"
                        @change="redrawCanvas"
                        :min="0"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item label="Y" size="small">
                      <a-input-number
                        v-model:value="selectedArea.y"
                        size="small"
                        @change="redrawCanvas"
                        :min="0"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item label="Width" size="small">
                      <a-input-number
                        v-model:value="selectedArea.width"
                        size="small"
                        @change="redrawCanvas"
                        :min="10"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-form-item label="Height" size="small">
                      <a-input-number
                        v-model:value="selectedArea.height"
                        size="small"
                        @change="redrawCanvas"
                        :min="10"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </div>

            <!-- Blur Areas List -->
            <div class="blur-areas-list" v-if="blurAreas.length > 0">
              <h4>Blur/Remove Areas:</h4>
              <a-list size="small" :data-source="blurAreas">
                <template #renderItem="{ item, index }">
                  <a-list-item>
                    <template #actions>
                      <a-button
                        size="small"
                        type="text"
                        danger
                        @click="removeBlurArea(index)"
                      >
                        <delete-outlined />
                      </a-button>
                    </template>
                    <a-list-item-meta>
                      <template #title>
                        {{ item.type.charAt(0).toUpperCase() + item.type.slice(1) }} Area {{ index + 1 }}
                      </template>
                      <template #description>
                        Position: {{ Math.round(item.x) }}, {{ Math.round(item.y) }} -
                        Size: {{ Math.round(item.width) }} x {{ Math.round(item.height) }}<br>
                        Time: {{ item.timeStart }}s - {{ item.timeEnd }}s
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>

            <!-- Time Range Controls -->
            <div class="time-controls">
              <h4>Apply to Time Range:</h4>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="Start Time (seconds)">
                    <a-input-number
                      v-model:value="timeRange.start"
                      :min="0"
                      :max="videoDuration"
                      :step="0.1"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="End Time (seconds)">
                    <a-input-number
                      v-model:value="timeRange.end"
                      :min="0"
                      :max="videoDuration"
                      :step="0.1"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-button @click="setCurrentTimeAsStart" size="small" style="margin-right: 8px">
                Set Current as Start
              </a-button>
              <a-button @click="setCurrentTimeAsEnd" size="small">
                Set Current as End
              </a-button>
            </div>
          </div>
        </a-col>

        <!-- Options Column -->
        <a-col :span="12">
          <a-form
            :model="renderOptions"
            layout="vertical"
          >
        <!-- Text Options -->
        <a-form-item label="Text Animation">
          <a-checkbox v-model:checked="renderOptions.showText">
            Show text Animation
          </a-checkbox>
          <div v-if="renderOptions.showText" class="mt-2">

            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="Text">
                  <a-input
                    v-model:value="renderOptions.textValue"
                    type="text"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="Font Size">
                  <a-input-number
                    v-model:value="renderOptions.fontSize"
                    :min="12"
                    :max="72"
                    addon-after="px"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="Text Color">
                  <a-input
                    v-model:value="renderOptions.textColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <!-- opacity -->
              <a-col :span="5">
                <a-form-item label="Opacity">
                  <a-slider
                    v-model:value="renderOptions.textOpacity"
                    :min="0"
                    :max="100"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form-item>
        <a-form-item label="Subtitles Settings">
          <a-checkbox v-model:checked="renderOptions.showSubtitle">
            Show subtitles
          </a-checkbox>
          <div v-if="renderOptions.showSubtitle" class="mt-2">
            <!-- subtitle preview -->
            <!-- {
            fontSize: 56,
            textColor: cssToASSColor('#FFFFFF'),
            backgroundColor: cssToASSColor('#000000', '80'),
            borderStyle: 3,
            bold: true,
            addPadding: true,
            alignment: 2,
            marginVertical: 50,
          } -->
            <!-- Color Presets -->
            <div class="color-presets-section mb-4">
              <h4 class="mb-2">Color Presets:</h4>
              <div class="color-presets-grid">
                <div
                  v-for="preset in subtitleColorPresets"
                  :key="preset.name"
                  class="color-preset-item"
                  @click="applyColorPreset(preset)"
                  :class="{
                    'active': renderOptions.subtitleTextColor === preset.textColor &&
                             renderOptions.subtitleBackgroundColor === preset.backgroundColor
                  }"
                >
                  <div
                    class="color-preview"
                    :style="{
                      color: preset.textColor,
                      backgroundColor: preset.backgroundColor === 'transparent' ? 'rgba(0,0,0,0.1)' : preset.backgroundColor,
                      textShadow: preset.borderColor === 'transparent'
                        ? 'none'
                        : `
                          -1px -1px 0 ${preset.borderColor},
                          1px -1px 0 ${preset.borderColor},
                          -1px  1px 0 ${preset.borderColor},
                          1px  1px 0 ${preset.borderColor},
                          -2px  0px 0 ${preset.borderColor},
                          2px  0px 0 ${preset.borderColor},
                          0px -2px 0 ${preset.borderColor},
                          0px  2px 0 ${preset.borderColor}
                        `,

                      fontWeight: 'bold',
                      fontSize: '18px',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      textAlign: 'center',
                      cursor: 'pointer',
                      minHeight: '24px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }"
                  >
                    {{ preset.name === 'None' ? '⊘' : 'Text' }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Subtitle Preview -->
            <div class="subtitle-preview h-12 mb-4">
              <p
                class="subtitle-preview-content"
                :style="{
                  fontSize: `${renderOptions.subtitleFontSize / 2}px`,
                  color: renderOptions.subtitleTextColor,
                  backgroundColor: renderOptions.subtitleBackgroundColor === 'transparent' ? 'rgba(0,0,0,0.0)' : renderOptions.subtitleBackgroundColor,
                  textAlign: 'center',
                  fontWeight: renderOptions.subtitleBold ? 'bold' : 'normal',
                  _border: `2px solid ${renderOptions.subtitleBorderColor}`,
                  padding: '2px',
                  textShadow:
                    renderOptions.subtitleBorderColor === 'transparent'
                      ? 'none'
                      : `
                    -1px -1px 0 ${renderOptions.subtitleBorderColor},
                    1px -1px 0 ${renderOptions.subtitleBorderColor},
                    -1px  1px 0 ${renderOptions.subtitleBorderColor},
                    1px  1px 0 ${renderOptions.subtitleBorderColor},
                    -2px  0px 0 ${renderOptions.subtitleBorderColor},
                    2px  0px 0 ${renderOptions.subtitleBorderColor},
                    0px -2px 0 ${renderOptions.subtitleBorderColor},
                    0px  2px 0 ${renderOptions.subtitleBorderColor}
                  `,
                }"
              >
                {{ ttsStore.currentSrtList?.items[0]?.translatedText || ttsStore.currentSrtList?.items[0]?.text || 'Sample Subtitle Text' }}
              </p>
            </div>
            <a-row :gutter="16">
              <a-col :span="5">
                <a-form-item label="Font Size">
                  <a-input-number
                    v-model:value="renderOptions.subtitleFontSize"
                    :min="12"
                    :max="72"
                    addon-after="px"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="Text Color">
                  <a-input
                    v-model:value="renderOptions.subtitleTextColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="Background">
                  <a-input
                    v-model:value="renderOptions.subtitleBackgroundColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="Border Color">
                  <a-input
                    v-model:value="renderOptions.subtitleBorderColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <!-- bold -->
              <a-col :span="3">
                <a-form-item label="Bold">
                  <a-checkbox v-model:checked="renderOptions.subtitleBold">
                    Bold
                  </a-checkbox>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- ASS Colors Debug -->
            <a-row :gutter="16" class="mt-2">
              <a-col :span="24">
                <a-button
                  size="small"
                  type="dashed"
                  @click="() => { console.log('Current ASS Colors:', getCurrentASSColors()); message.info('ASS colors logged to console'); }"
                >
                  Show ASS Colors (Console)
                </a-button>
              </a-col>
            </a-row>
          </div>
        </a-form-item>
        <!-- Logo Options -->
        <a-form-item label="Logo Settings">
          <a-checkbox v-model:checked="renderOptions.showLogo">
            Add logo/watermark
          </a-checkbox>
          <div v-if="renderOptions.showLogo" class="mt-2">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="Logo Position">
                  <a-select v-model:value="renderOptions.logoPosition">
                    <a-select-option value="top-left">Top Left</a-select-option>
                    <a-select-option value="top-right">Top Right</a-select-option>
                    <a-select-option value="bottom-left">Bottom Left</a-select-option>
                    <a-select-option value="bottom-right">Bottom Right</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="Logo Size">
                  <a-select v-model:value="renderOptions.logoSize">
                    <a-select-option value="small">Small</a-select-option>
                    <a-select-option value="medium">Medium</a-select-option>
                    <a-select-option value="large">Large</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="Logo File">
              <a-upload
                v-model:file-list="logoFileList"
                :before-upload="beforeLogoUpload"
                accept="image/*"
                :max-count="1"
              >
                <a-button>
                  <upload-outlined />
                  Select Logo
                </a-button>
              </a-upload>
            </a-form-item>
          </div>
        </a-form-item>

        <!-- Audio Options -->
        <a-form-item label="Audio Settings" class="border border-gray-600 p-2 rounded-lg">
          <a-checkbox v-model:checked="renderOptions.addBackgroundMusic">
            Add background music
          </a-checkbox>
          <div v-if="renderOptions.addBackgroundMusic" class="mt-2">
            <a-form-item label="Background Music File">
              <a-upload
                v-model:file-list="musicFileList"
                :before-upload="beforeMusicUpload"
                accept="audio/*"
                :max-count="1"
              >
                <a-button>
                  <upload-outlined />
                  Select Music
                </a-button>
              </a-upload>
            </a-form-item>
            <a-form-item label="Background Music Volume">
              <a-slider
                v-model:value="renderOptions.backgroundMusicVolume"
                :min="0"
                :max="100"
                :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
              />
            </a-form-item>
          </div>

          <a-form-item label="Âm thanh video gốc" class="mt-2">
            <a-checkbox v-model:checked="renderOptions.holdMusicOnly">
              Chỉ giữ lại nhạc nền
            </a-checkbox>
            <a-checkbox v-model:checked="renderOptions.holdOriginalAudio">
              Giữ lại âm thanh video gốc
            </a-checkbox>
          </a-form-item>
          <!-- âm lượng âm thanh video gốc -->
          <a-form-item label="Âm lượng âm thanh video gốc" v-if="renderOptions.holdOriginalAudio">
            <a-slider
              v-model:value="renderOptions.originalAudioVolume"
              :min="0"
              :max="100"
              :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
            />
          </a-form-item>
        </a-form-item>

        <!-- Output Options -->
        <a-form-item label="Output Settings">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="Video Quality">
                <a-select v-model:value="renderOptions.videoQuality">
                  <a-select-option value="720p/16:9">720p HD</a-select-option>
                  <a-select-option value="1080p/16:9">Full HD</a-select-option>
                  <a-select-option value="4k/16:9">4K Ultra HD</a-select-option>
                  <a-select-option value="1080p/9:16">Full HD Dọc</a-select-option>
                  <a-select-option value="4k/9:16">4K Ultra HD Dọc</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="Frame Rate">
                <a-select v-model:value="renderOptions.frameRate">
                  <a-select-option value="24">24 fps</a-select-option>
                  <a-select-option value="30">30 fps</a-select-option>
                  <a-select-option value="60">60 fps</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
        </a-form>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, reactive, nextTick, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  UploadOutlined,
  // BlurOutlined,
  DeleteOutlined,
  FontColorsOutlined,
  ClearOutlined,
  UndoOutlined,
  DragOutlined
} from '@ant-design/icons-vue';
import { useTTSStore } from '@/stores/ttsStore';
import VideoPlayer from './VideoPlayer.vue';

const ttsStore = useTTSStore();
const isProcessing = ref(false);
const modalVisible = ref(false);
const logoFileList = ref([]);
const musicFileList = ref([]);

// Video preview refs
const videoPlayer = ref(null);
const drawingCanvas = ref(null);
const videoSrc = ref(ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4'));  // Will be set from ttsStore or file selection
const videoDuration = ref(0);
const currentTime = ref(0);

// Drawing state
const isDrawing = ref(false);
const isResizing = ref(false);
const drawingMode = ref('subtitle'); // 'blur', 'delogo', 'subtitle'
const blurAreas = ref([]);
const currentDrawing = ref(null);
const drawingHistory = ref([]);
const selectedArea = ref(null);
const resizeHandle = ref(null); // 'nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'
const isDragging = ref(false);

// Time range for applying effects
const timeRange = reactive({
  start: 0,
  end: 0
});

// Render options configuration
const renderOptions = reactive({
  // Text options
  showText: true,
  fontSize: 24,
  textColor: '#fff700',
  textValue: '@Hello World',
  textOpacity: 50,

  // Text Subtitle options
  showSubtitle: true,
  subtitleFontSize: 48,
  subtitleTextColor: '#ffffff',
  subtitleBackgroundColor: '#000000',
  subtitleBorderColor: '#000000',
  subtitleBold: true,
  shadowSize: 2,

  // Logo options
  showLogo: false,
  logoPosition: 'bottom-right',
  logoSize: 'medium',

  // Audio options
  addBackgroundMusic: false,
  backgroundMusicVolume: 30,
  originalAudioVolume: 80,
  holdOriginalAudio: false,
  holdMusicOnly: false,

  // Output options
  videoQuality: '1080p/16:9',
  frameRate: '30'
});

const canRender = computed(() => {
  return ttsStore.currentSrtList && ttsStore.currentSrtList.items.length > 0;
});

// Subtitle color presets
const subtitleColorPresets = [
  // { name: 'None', textColor: 'transparent', backgroundColor: 'transparent', borderColor: 'transparent' },
  { name: 'White', textColor: '#FFFFFF', backgroundColor: '#000000', borderColor: '#000000' },
  { name: 'Black', textColor: '#000000', backgroundColor: '#FFFFFF', borderColor: '#FFFFFF' },
  { name: 'Blue', textColor: '#FFFFFF', backgroundColor: '#0066CC', borderColor: 'transparent' },
  { name: 'Purple', textColor: '#FFFFFF', backgroundColor: '#6600CC', borderColor: 'transparent' },
  { name: 'Yellow', textColor: '#000000', backgroundColor: '#FFCC00', borderColor: 'transparent' },
  { name: 'Blue Glow', textColor: '#00CCFF', backgroundColor: 'transparent', borderColor: '#0066CC' },
  { name: 'White Glow', textColor: '#FFFFFF', backgroundColor: 'transparent', borderColor: '#000000' },
  { name: 'Green', textColor: '#00FF00', backgroundColor: 'transparent', borderColor: '#006600' },
  { name: 'Pink', textColor: '#FF66CC', backgroundColor: 'transparent', borderColor: '#CC3399' },
  { name: 'Rainbow', textColor: '#FF6600', backgroundColor: 'transparent', borderColor: '#CC3300' },
  { name: 'Gold', textColor: '#FFD700', backgroundColor: 'transparent', borderColor: '#B8860B' }
];

// Convert CSS color to ASS color format
function cssToASSColor(cssColor, opacity = '00') {
  // Remove # if present
  const hex = cssColor.replace('#', '');

  if (hex === 'transparent' || hex === '') {
    return '&HFF000000';
  }

  // Convert hex to RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // ASS format: &H + opacity + BB + GG + RR (BGR format)
  const assColor = `&H${opacity}${b.toString(16).padStart(2, '0').toUpperCase()}${g.toString(16).padStart(2, '0').toUpperCase()}${r.toString(16).padStart(2, '0').toUpperCase()}`;

  return assColor;
}

// Apply color preset to subtitle options
function applyColorPreset(preset) {
  renderOptions.subtitleTextColor = preset.textColor;
  renderOptions.subtitleBackgroundColor = preset.backgroundColor;
  renderOptions.subtitleBorderColor = preset.borderColor;

  console.log('Applied color preset:', preset.name);
  console.log('ASS Colors:', {
    text: cssToASSColor(preset.textColor),
    background: cssToASSColor(preset.backgroundColor, '90'),
    border: cssToASSColor(preset.borderColor)
  });

  message.success(`Applied ${preset.name} color preset`);
}

// Get current ASS colors for debugging
function getCurrentASSColors() {
  return {
    text: cssToASSColor(renderOptions.subtitleTextColor),
    background: cssToASSColor(renderOptions.subtitleBackgroundColor, '90'),
    border: cssToASSColor(renderOptions.subtitleBorderColor)
  };
}


// blurAreas init load to canvas
onMounted(() => {
  redrawCanvas();
});



function showRenderModal() {
  if (!canRender.value) return;
  modalVisible.value = true;

  // Load video source from ttsStore or show file selector
  nextTick(() => {
    initializeVideoPreview();
  });
}

function handleModalCancel() {
  modalVisible.value = false;
  clearDrawings();
}

function beforeLogoUpload(file) {
  // Validate logo file
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('Please select a valid image file!');
    return false;
  }

  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('Logo file must be smaller than 5MB!');
    return false;
  }

  renderOptions.logoFile = file;
  return false; // Prevent auto upload
}

function beforeMusicUpload(file) {
  // Validate music file
  const isAudio = file.type.startsWith('audio/');
  if (!isAudio) {
    message.error('Please select a valid audio file!');
    return false;
  }

  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('Music file must be smaller than 50MB!');
    return false;
  }

  renderOptions.musicFile = file;
  return false; // Prevent auto upload
}

async function handleRenderConfirm() {
  isProcessing.value = true;

  try {
    // Prepare render configuration
    const renderConfig = {
      srtItems: ttsStore.currentSrtList.items,
      srtPath: ttsStore.currentSrtList.path,
      blurAreas: blurAreas.value,
      timeRange: timeRange,
      options: {
        textAnimation: {
          enabled: renderOptions.showText,
          fontSize: renderOptions.fontSize,
          color: renderOptions.textColor,
          value: renderOptions.textValue,
          opacity: renderOptions.textOpacity / 100
        },
        textSubtitle: {
          enabled: renderOptions.showSubtitle,
          fontSize: renderOptions.subtitleFontSize,
          color: renderOptions.subtitleTextColor,
          backgroundColor: renderOptions.subtitleBackgroundColor,
          borderColor: renderOptions.subtitleBorderColor,
          bold: renderOptions.subtitleBold,
          // ASS format colors for advanced subtitle rendering
          assColors: {
            text: cssToASSColor(renderOptions.subtitleTextColor),
            background: cssToASSColor(renderOptions.subtitleBackgroundColor),
            border: cssToASSColor(renderOptions.subtitleBorderColor),
            shadowSize: renderOptions.shadowSize
          }
        },
        logo: {
          enabled: renderOptions.showLogo,
          position: renderOptions.logoPosition,
          size: renderOptions.logoSize,
          file: renderOptions.logoFile
        },
        audio: {
          backgroundMusic: {
            enabled: renderOptions.addBackgroundMusic,
            file: renderOptions.musicFile,
            volume: renderOptions.backgroundMusicVolume / 100
          },
          originalVolume: renderOptions.originalAudioVolume / 100,
          holdOriginalAudio: renderOptions.holdOriginalAudio,
          holdMusicOnly: renderOptions.holdMusicOnly
        },
        output: {
          quality: renderOptions.videoQuality,
          frameRate: parseInt(renderOptions.frameRate)
        }
      }
    };
    console.log('renderConfig', renderConfig);
    // Call the electron API with enhanced options
    electronAPI.processVideoWithOptions(JSON.parse(JSON.stringify(renderConfig)));

    // Simulate processing time
    await new Promise((resolve) => setTimeout(resolve, 2000));

    message.success('Video rendered successfully with custom options and blur areas!');
    modalVisible.value = false;

  } catch (error) {
    message.error('Error rendering video: ' + error.message);
  } finally {
    isProcessing.value = false;
  }
}

// Video preview functions
async function initializeVideoPreview() {
  // Try to get video source from ttsStore or electron API
  try {
    if (ttsStore.currentSrtList?.videoPath) {
      videoSrc.value = ttsStore.currentSrtList.videoPath;
    } else {
      // Request video file from electron
      const videoPath = await electronAPI.getVideoPath();
      if (videoPath) {
        videoSrc.value = videoPath;
      }
    }
  } catch (error) {
    console.warn('Could not load video preview:', error);
  }
}

function onVideoLoaded() {
  if (videoPlayer.value.$refs.video) {
    videoDuration.value = videoPlayer.value.$refs.video.duration;
    timeRange.end = videoDuration.value;
    setupCanvas();
    initializeDefaultBlurArea();
  }
}

function onVideoTimeUpdate() {
  if (videoPlayer.value.$refs.video) {
    currentTime.value = videoPlayer.value.$refs.video.currentTime;
  }
}

function setupCanvas() {
  if (drawingCanvas.value && videoPlayer.value.$refs.video) {
    const canvas = drawingCanvas.value;
    const video = videoPlayer.value.$refs.video;

    canvas.width = video.clientWidth;
    canvas.height = video.clientHeight;
  }
}

// Initialize default blur area with automatic timeEnd
function initializeDefaultBlurArea() {
  if (videoDuration.value <= 0) {
    message.warning('Please wait for video to load completely');
    return;
  }

  const canvas = drawingCanvas.value;
  if (!canvas) {
    message.warning('Canvas not ready');
    return;
  }

  // If no blur areas exist, create a default subtitle area
  if (blurAreas.value.length === 0) {
    const defaultArea = {
      type: "subtitle",
      x: canvas.width * 0.01, // 10% from left
      y: canvas.height * 0.85, // 80% from top (bottom area)
      width: canvas.width * 0.97, // 80% of video width
      height: canvas.height * 0.1, // 10% of video height
      timeStart: 0,
      timeEnd: videoDuration.value // Automatically use video duration
    };

    blurAreas.value.push(defaultArea);
    console.log('Initialized default blur area:', defaultArea);
    message.success('Default subtitle area added');
  } else {
    // If areas exist, add a new area based on current drawing mode
    const areaCount = blurAreas.value.length;
    const offset = (areaCount * 20) % 100; // Offset each new area slightly

    const newArea = {
      type: drawingMode.value,
      x: (canvas.width * 0.1) + offset,
      y: (canvas.height * 0.7) + offset,
      width: canvas.width * 0.3,
      height: canvas.height * 0.15,
      timeStart: timeRange.start,
      timeEnd: timeRange.end || videoDuration.value
    };

    blurAreas.value.push(newArea);
    console.log('Added new area:', newArea);
    message.success(`New ${drawingMode.value} area added`);
  }

  // Update existing areas' timeEnd if they don't have proper values
  blurAreas.value.forEach(area => {
    if (!area.timeEnd || area.timeEnd === 0 || area.timeEnd > videoDuration.value) {
      area.timeEnd = videoDuration.value;
    }
  });

  // Redraw canvas to show the new area
  nextTick(() => {
    redrawCanvas();
  });
}

// Drawing functions
function setDrawingMode(mode) {
  drawingMode.value = mode;
  if (mode !== 'select') {
    selectedArea.value = null;
    redrawCanvas();
  }
}

function handleMouseDown(e) {
  if (!drawingCanvas.value) return;

  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  if (drawingMode.value === 'select') {
    // Check if clicking on resize handle first
    const handle = getResizeHandle(x, y);
    if (handle && selectedArea.value) {
      startResize(handle, x, y);
      return;
    }

    // Check if clicking inside an existing area
    const area = getAreaAtPoint(x, y);
    if (area) {
      selectedArea.value = area;
      startDrag(x, y);
      redrawCanvas();
      return;
    } else {
      selectedArea.value = null;
      redrawCanvas();
    }
  } else {
    // Start drawing new area
    startDrawing(x, y);
  }
}

function handleMouseMove(e) {
  if (!drawingCanvas.value) return;

  const rect = drawingCanvas.value.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  // Update cursor based on context
  updateCursor(x, y);

  if (isDrawing.value) {
    continueDrawing(x, y);
  } else if (isResizing.value) {
    continueResize(x, y);
  } else if (isDragging.value) {
    continueDrag(x, y);
  }
}

function handleMouseUp() {
  if (isDrawing.value) {
    stopDrawing();
  } else if (isResizing.value) {
    stopResize();
  } else if (isDragging.value) {
    stopDrag();
  }
}

function handleMouseLeave() {
  handleMouseUp();
}

function handleCanvasClick(e) {
  // Handle single clicks for selection
  if (drawingMode.value === 'select' && !isDrawing.value && !isResizing.value && !isDragging.value) {
    const rect = drawingCanvas.value.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const area = getAreaAtPoint(x, y);
    selectedArea.value = area;
    redrawCanvas();
  }
}

function startDrawing(x, y) {
  isDrawing.value = true;
  currentDrawing.value = {
    type: drawingMode.value,
    x,
    y,
    width: 0,
    height: 0,
    timeStart: timeRange.start,
    timeEnd: timeRange.end
  };
  drawRectangle();
}

function continueDrawing(x, y) {
  if (!currentDrawing.value) return;

  currentDrawing.value.width = x - currentDrawing.value.x;
  currentDrawing.value.height = y - currentDrawing.value.y;
  drawRectangle();
}

function stopDrawing() {
  if (!isDrawing.value || !currentDrawing.value) return;

  isDrawing.value = false;

  // Only add if rectangle has meaningful size
  if (Math.abs(currentDrawing.value.width) > 10 && Math.abs(currentDrawing.value.height) > 10) {
    // Normalize negative dimensions
    if (currentDrawing.value.width < 0) {
      currentDrawing.value.x += currentDrawing.value.width;
      currentDrawing.value.width = Math.abs(currentDrawing.value.width);
    }
    if (currentDrawing.value.height < 0) {
      currentDrawing.value.y += currentDrawing.value.height;
      currentDrawing.value.height = Math.abs(currentDrawing.value.height);
    }

    blurAreas.value.push({ ...currentDrawing.value });
    drawingHistory.value.push([...blurAreas.value]);
  }

  currentDrawing.value = null;
  redrawCanvas();
}

// Resize functions
function startResize(handle, x, y) {
  isResizing.value = true;
  resizeHandle.value = handle;
  resizeStartPos = { x, y };
  resizeStartArea = { ...selectedArea.value };
}

function continueResize(x, y) {
  if (!selectedArea.value || !resizeHandle.value) return;

  const deltaX = x - resizeStartPos.x;
  const deltaY = y - resizeStartPos.y;
  const area = selectedArea.value;

  switch (resizeHandle.value) {
    case 'nw': // Top-left
      area.x = resizeStartArea.x + deltaX;
      area.y = resizeStartArea.y + deltaY;
      area.width = resizeStartArea.width - deltaX;
      area.height = resizeStartArea.height - deltaY;
      break;
    case 'ne': // Top-right
      area.y = resizeStartArea.y + deltaY;
      area.width = resizeStartArea.width + deltaX;
      area.height = resizeStartArea.height - deltaY;
      break;
    case 'sw': // Bottom-left
      area.x = resizeStartArea.x + deltaX;
      area.width = resizeStartArea.width - deltaX;
      area.height = resizeStartArea.height + deltaY;
      break;
    case 'se': // Bottom-right
      area.width = resizeStartArea.width + deltaX;
      area.height = resizeStartArea.height + deltaY;
      break;
    case 'n': // Top
      area.y = resizeStartArea.y + deltaY;
      area.height = resizeStartArea.height - deltaY;
      break;
    case 's': // Bottom
      area.height = resizeStartArea.height + deltaY;
      break;
    case 'w': // Left
      area.x = resizeStartArea.x + deltaX;
      area.width = resizeStartArea.width - deltaX;
      break;
    case 'e': // Right
      area.width = resizeStartArea.width + deltaX;
      break;
  }

  // Ensure minimum size
  if (area.width < 10) area.width = 10;
  if (area.height < 10) area.height = 10;

  redrawCanvas();
}

function stopResize() {
  isResizing.value = false;
  resizeHandle.value = null;
}

// Drag functions
let dragStartPos = { x: 0, y: 0 };
let dragStartArea = null;
let resizeStartPos = { x: 0, y: 0 };
let resizeStartArea = null;

function startDrag(x, y) {
  isDragging.value = true;
  dragStartPos = { x, y };
  dragStartArea = { ...selectedArea.value };
}

function continueDrag(x, y) {
  if (!selectedArea.value) return;

  const deltaX = x - dragStartPos.x;
  const deltaY = y - dragStartPos.y;

  selectedArea.value.x = dragStartArea.x + deltaX;
  selectedArea.value.y = dragStartArea.y + deltaY;

  redrawCanvas();
}

function stopDrag() {
  isDragging.value = false;
}

// Helper functions
function getAreaAtPoint(x, y) {
  for (let i = blurAreas.value.length - 1; i >= 0; i--) {
    const area = blurAreas.value[i];
    if (x >= area.x && x <= area.x + area.width &&
        y >= area.y && y <= area.y + area.height) {
      return area;
    }
  }
  return null;
}

function getResizeHandle(x, y) {
  if (!selectedArea.value) return null;

  const area = selectedArea.value;
  const handleSize = 8;

  // Corner handles
  if (isNearPoint(x, y, area.x, area.y, handleSize)) return 'nw';
  if (isNearPoint(x, y, area.x + area.width, area.y, handleSize)) return 'ne';
  if (isNearPoint(x, y, area.x, area.y + area.height, handleSize)) return 'sw';
  if (isNearPoint(x, y, area.x + area.width, area.y + area.height, handleSize)) return 'se';

  // Edge handles
  if (isNearPoint(x, y, area.x + area.width/2, area.y, handleSize)) return 'n';
  if (isNearPoint(x, y, area.x + area.width/2, area.y + area.height, handleSize)) return 's';
  if (isNearPoint(x, y, area.x, area.y + area.height/2, handleSize)) return 'w';
  if (isNearPoint(x, y, area.x + area.width, area.y + area.height/2, handleSize)) return 'e';

  return null;
}

function isNearPoint(x, y, targetX, targetY, threshold) {
  return Math.abs(x - targetX) <= threshold && Math.abs(y - targetY) <= threshold;
}

function updateCursor(x, y) {
  if (!drawingCanvas.value) return;

  let cursor = 'default';

  if (drawingMode.value === 'select') {
    const handle = getResizeHandle(x, y);
    if (handle) {
      const cursors = {
        'nw': 'nw-resize', 'ne': 'ne-resize',
        'sw': 'sw-resize', 'se': 'se-resize',
        'n': 'n-resize', 's': 's-resize',
        'w': 'w-resize', 'e': 'e-resize'
      };
      cursor = cursors[handle];
    } else if (getAreaAtPoint(x, y)) {
      cursor = 'move';
    }
  } else {
    cursor = 'crosshair';
  }

  drawingCanvas.value.style.cursor = cursor;
}

function drawRectangle() {
  if (!drawingCanvas.value || !currentDrawing.value) return;

  const canvas = drawingCanvas.value;
  const ctx = canvas.getContext('2d');

  // Clear and redraw all areas
  redrawCanvas();

  // Draw current rectangle being drawn
  ctx.strokeStyle = getDrawingColor(currentDrawing.value.type);
  ctx.fillStyle = getDrawingColor(currentDrawing.value.type, 0.3);
  ctx.lineWidth = 2;
  ctx.setLineDash([5, 5]);

  ctx.fillRect(
    currentDrawing.value.x,
    currentDrawing.value.y,
    currentDrawing.value.width,
    currentDrawing.value.height
  );
  ctx.strokeRect(
    currentDrawing.value.x,
    currentDrawing.value.y,
    currentDrawing.value.width,
    currentDrawing.value.height
  );
}

function redrawCanvas() {
  if (!drawingCanvas.value) return;

  const canvas = drawingCanvas.value;
  const ctx = canvas.getContext('2d');

  // Clear canvas
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // Draw existing blur areas
  blurAreas.value.forEach((area, index) => {
    const isSelected = selectedArea.value === area;

    ctx.strokeStyle = getDrawingColor(area.type);
    ctx.fillStyle = getDrawingColor(area.type, 0.3);
    ctx.lineWidth = isSelected ? 3 : 2;
    ctx.setLineDash(isSelected ? [3, 3] : []);

    ctx.fillRect(area.x, area.y, area.width, area.height);
    ctx.strokeRect(area.x, area.y, area.width, area.height);

    // Add label
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.fillText(
      `${area.type} ${index + 1}`,
      area.x + 5,
      area.y + 15
    );

    // Draw resize handles for selected area
    if (isSelected && drawingMode.value === 'select') {
      drawResizeHandles(ctx, area);
    }
  });
}

function drawResizeHandles(ctx, area) {
  const handleSize = 6;
  const handles = [
    { x: area.x, y: area.y }, // nw
    { x: area.x + area.width, y: area.y }, // ne
    { x: area.x, y: area.y + area.height }, // sw
    { x: area.x + area.width, y: area.y + area.height }, // se
    { x: area.x + area.width/2, y: area.y }, // n
    { x: area.x + area.width/2, y: area.y + area.height }, // s
    { x: area.x, y: area.y + area.height/2 }, // w
    { x: area.x + area.width, y: area.y + area.height/2 } // e
  ];

  ctx.fillStyle = '#1890ff';
  ctx.strokeStyle = '#fff';
  ctx.lineWidth = 1;
  ctx.setLineDash([]);

  handles.forEach(handle => {
    ctx.fillRect(
      handle.x - handleSize/2,
      handle.y - handleSize/2,
      handleSize,
      handleSize
    );
    ctx.strokeRect(
      handle.x - handleSize/2,
      handle.y - handleSize/2,
      handleSize,
      handleSize
    );
  });
}

function getDrawingColor(type, alpha = 1) {
  const colors = {
    blur: `rgba(255, 255, 0, ${alpha})`, // Yellow
    delogo: `rgba(255, 0, 0, ${alpha})`, // Red
    subtitle: `rgba(0, 255, 0, ${alpha})` // Green
  };
  return colors[type] || `rgba(255, 255, 255, ${alpha})`;
}

function clearDrawings() {
  blurAreas.value = [];
  drawingHistory.value = [];
  selectedArea.value = null;
  redrawCanvas();
}

function undoLastDrawing() {
  if (blurAreas.value.length > 0) {
    blurAreas.value.pop();
    redrawCanvas();
  }
}

function removeBlurArea(index) {
  blurAreas.value.splice(index, 1);
  redrawCanvas();
}

function setCurrentTimeAsStart() {
  timeRange.start = currentTime.value;
}

function setCurrentTimeAsEnd() {
  timeRange.end = currentTime.value;
}
</script>

<style scoped>
.mt-2 {
  margin-top: 8px;
}

.video-preview-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  /* background: #fafafa; */
}

.video-container {
  position: relative;
  margin-bottom: 16px;
  border-radius: 4px;
  overflow: hidden;
}

.preview-video {
  width: 100%;
  height: auto;
  max-height: 300px;
  background: #000;
}

.text-preview {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 9;
}

.text-preview-content {
  /* max-width: 90%; */
  /* word-wrap: break-word; */
  /* overflow-y: auto; */
  /* padding: 10px; */
  /* background: rgba(0, 0, 0, 0.5); */
  /* color: white; */
  /* border-radius: 5px; */
}


.drawing-canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.drawing-controls {
  margin-bottom: 16px;
  padding: 12px;
  /* background: #fff; */
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.selected-area-info {
  margin-top: 12px;
}

.blur-areas-list {
  margin-bottom: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.time-controls {
  padding: 12px;
  /* background: #fff; */
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.time-controls h4 {
  margin-bottom: 12px;
  color: #1890ff;
}

/* Color Presets Styles */
.color-presets-section {
  margin-bottom: 16px;
}

.color-presets-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.color-preset-item {
  position: relative;
}

.color-preset-item.active .color-preview {
  box-shadow: 0 0 0 3px #1890ff;
  transform: scale(1.05);
}

.color-preview {
  transition: all 0.2s ease;
  user-select: none;
}

.color-preview:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.subtitle-preview {
  /* border: 1px solid #d9d9d9; */
  border-radius: 4px;
  padding: 8px;
  /* background: #f5f5f5; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.subtitle-preview-content {
  margin: 0;
  max-width: 100%;
  word-wrap: break-word;
}
</style>