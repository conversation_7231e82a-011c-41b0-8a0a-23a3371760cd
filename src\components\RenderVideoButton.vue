<template>
  <div class="select-none">
    <!-- Ren<PERSON>ton -->
    <a-button
      type="primary"
      @click="showRenderModal"
      :disabled="!canRender || isProcessing"
      :loading="isProcessing"
    >
      Render Video
    </a-button>

    <!-- Render Options Modal -->
    <a-modal
      v-model:open="modalVisible"
      title="Video Render Options"
      @ok="handleRenderConfirm"
      @cancel="handleModalCancel"
      :confirm-loading="isProcessing"
      width="90%"
      :body-style="{ maxHeight: '80vh', overflow: 'auto' }"
      class="select-none"
    >
      <a-row :gutter="24">
        <!-- Video Preview Column -->
        <a-col :span="12">
          <VideoPreviewDraw
            ref="videoPreviewDraw"
            :video-src="ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')"
            :render-options="renderOptions"
            :time-range="timeRange"
            :video-duration="videoDuration"
            :current-time="currentTime"
            @blur-areas-updated="handleBlurAreasUpdated"
            @time-range-updated="handleTimeRangeUpdated"
            @video-loaded="onVideoLoaded"
            @video-time-update="onVideoTimeUpdate"
          />
        </a-col>

        <!-- Options Column -->
        <a-col :span="12">
          <a-form
            :model="renderOptions"
            layout="vertical"
          >
        <!-- Text Options -->
        <a-form-item label="Text Animation">
          <a-checkbox v-model:checked="renderOptions.showText">
            Show text Animation
          </a-checkbox>
          <div v-if="renderOptions.showText" class="mt-2">

            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="Text">
                  <a-input
                    v-model:value="renderOptions.textValue"
                    type="text"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="Font Size">
                  <a-input-number
                    v-model:value="renderOptions.fontSize"
                    :min="12"
                    :max="72"
                    addon-after="px"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="Text Color">
                  <a-input
                    v-model:value="renderOptions.textColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <!-- opacity -->
              <a-col :span="5">
                <a-form-item label="Opacity">
                  <a-slider
                    v-model:value="renderOptions.textOpacity"
                    :min="0"
                    :max="100"
                  />
                </a-form-item>
              </a-col>
              <!-- direction -->
              <a-col :span="4">
                <a-form-item label="Hướng di chuyển">
                  <a-select v-model:value="renderOptions.textDirection">
                    <a-select-option value="random">Random</a-select-option>
                    <a-select-option value="updown">Up & Down</a-select-option>
                    <a-select-option value="leftright">Left & Right</a-select-option>
                    <a-select-option value="diagonal">Diagonal</a-select-option>
                    <a-select-option value="all">All</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

            </a-row>
          </div>
        </a-form-item>
        <a-form-item label="Subtitles Settings">
          <a-checkbox v-model:checked="renderOptions.showSubtitle">
            Show subtitles
          </a-checkbox>
          <div v-if="renderOptions.showSubtitle" class="mt-2">
            <!-- Subtitle Preview -->
            <div class="subtitle-preview h-12 mb-1">
              <p
                class="subtitle-preview-content"
                :style="{
                  fontSize: `${renderOptions.subtitleFontSize / 2}px`,
                  color: renderOptions.subtitleTextColor,
                  backgroundColor: renderOptions.subtitleBackgroundColor === 'transparent' ? 'rgba(0,0,0,0.0)' : renderOptions.subtitleBackgroundColor,
                  textAlign: 'center',
                  fontWeight: renderOptions.subtitleBold ? 'bold' : 'normal',
                  _border: `2px solid ${renderOptions.subtitleBorderColor}`,
                  padding: '2px',
                  textShadow:
                    renderOptions.subtitleBorderColor === 'transparent'
                      ? 'none'
                      : `
                    -1px -1px 0 ${renderOptions.subtitleBorderColor},
                    1px -1px 0 ${renderOptions.subtitleBorderColor},
                    -1px  1px 0 ${renderOptions.subtitleBorderColor},
                    1px  1px 0 ${renderOptions.subtitleBorderColor},
                    -2px  0px 0 ${renderOptions.subtitleBorderColor},
                    2px  0px 0 ${renderOptions.subtitleBorderColor},
                    0px -2px 0 ${renderOptions.subtitleBorderColor},
                    0px  2px 0 ${renderOptions.subtitleBorderColor}
                  `,
                }"
              >
                {{ ttsStore.currentSrtList?.items[0]?.translatedText || ttsStore.currentSrtList?.items[0]?.text || 'Sample Subtitle Text' }}
              </p>
            </div>
            <!-- Color Presets -->
            <div class="color-presets-section mb-4">
              <h4 class="mb-2">Color Presets:</h4>
              <div class="color-presets-grid">
                <div
                  v-for="preset in subtitleColorPresets"
                  :key="preset.name"
                  class="color-preset-item"
                  @click="applyColorPreset(preset)"
                  :class="{
                    'active': renderOptions.subtitleTextColor === preset.textColor &&
                             renderOptions.subtitleBackgroundColor === preset.backgroundColor
                  }"
                >
                  <div
                    class="color-preview"
                    :style="{
                      color: preset.textColor,
                      backgroundColor: preset.backgroundColor === 'transparent' ? 'rgba(0,0,0,0.1)' : preset.backgroundColor,
                      textShadow: preset.borderColor === 'transparent'
                        ? 'none'
                        : `
                          -1px -1px 0 ${preset.borderColor},
                          1px -1px 0 ${preset.borderColor},
                          -1px  1px 0 ${preset.borderColor},
                          1px  1px 0 ${preset.borderColor},
                          -2px  0px 0 ${preset.borderColor},
                          2px  0px 0 ${preset.borderColor},
                          0px -2px 0 ${preset.borderColor},
                          0px  2px 0 ${preset.borderColor}
                        `,

                      fontWeight: 'bold',
                      fontSize: '18px',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      textAlign: 'center',
                      cursor: 'pointer',
                      minHeight: '24px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }"
                  >
                    {{ preset.name === 'None' ? '⊘' : 'Text' }}
                  </div>
                </div>
              </div>
            </div>


            <a-row :gutter="16">
              <a-col :span="5">
                <a-form-item label="Font Size">
                  <a-input-number
                    v-model:value="renderOptions.subtitleFontSize"
                    :min="12"
                    :max="72"
                    addon-after="px"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="5">
                <a-form-item label="Text Color">
                  <a-input
                    v-model:value="renderOptions.subtitleTextColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="Background">
                  <a-input
                    v-model:value="renderOptions.subtitleBackgroundColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="4">
                <a-form-item label="Border Color">
                  <a-input
                    v-model:value="renderOptions.subtitleBorderColor"
                    type="color"
                  />
                </a-form-item>
              </a-col>
              <!-- bold -->
              <a-col :span="3">
                <a-form-item label="Bold">
                  <a-checkbox v-model:checked="renderOptions.subtitleBold">
                    Bold
                  </a-checkbox>
                </a-form-item>
              </a-col>
            </a-row>
            <!-- assOptions subtitle in video -->
            <!-- left/right slider -->
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="Left/Right">
                  <a-slider
                    v-model:value="renderOptions.assOptions.posX"
                    :min="-100"
                    :max="100"
                    :marks="{ '-100': 'Left', '0': 'Center', '100': 'Right' }"
                    :step="1"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="Up/Down">
                  <a-slider
                    v-model:value="renderOptions.assOptions.posY"
                    :min="-100"
                    :max="100"
                    :marks="{ '-100': 'Top', '0': 'Center', '100': 'Bottom' }"
                    :step="1"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- Additional ASS Options -->
            <a-row :gutter="16" class="mt-2">
              <a-col :span="8">
                <a-form-item label="Rotation">
                  <a-slider
                    v-model:value="renderOptions.assOptions.rotation"
                    :min="-360"
                    :max="360"
                    :marks="{ '-360': '-360°', '0': '0°', '360': '360°' }"
                    :step="1"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="Alignment">
                  <a-select v-model:value="renderOptions.assOptions.align">
                      <a-select-option :value="7">Top Left</a-select-option>
                      <a-select-option :value="8">Top Center</a-select-option>
                      <a-select-option :value="9">Top Right</a-select-option>
                      <a-select-option :value="4">Middle Left</a-select-option>
                      <a-select-option :value="5">Middle Center</a-select-option>
                      <a-select-option :value="6">Middle Right</a-select-option>
                      <a-select-option :value="1">Bottom Left</a-select-option>
                      <a-select-option :value="2">Bottom Center</a-select-option>
                      <a-select-option :value="3">Bottom Right</a-select-option>

                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="Font Size Override">
                  <a-input-number
                    v-model:value="renderOptions.assOptions.fontSize"
                    :min="12"
                    :max="200"
                    addon-after="px"
                    placeholder="Auto"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <!-- ASS Colors Debug -->
            <a-row :gutter="16" class="mt-2">
              <a-col :span="24">
                <a-button
                  size="small"
                  type="dashed"
                  @click="() => { console.log('Current ASS Colors:', getCurrentASSColors()); message.info('ASS colors logged to console'); }"
                >
                  Show ASS Colors (Console)
                </a-button>
              </a-col>
            </a-row>
          </div>
        </a-form-item>
        <!-- Logo Options -->
        <a-form-item label="Logo Settings">
          <a-checkbox v-model:checked="renderOptions.showLogo">
            Add logo/watermark
          </a-checkbox>
          <div v-if="renderOptions.showLogo" class="mt-2">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="Logo Position">
                  <a-select v-model:value="renderOptions.logoPosition">
                    <a-select-option value="top-left">Top Left</a-select-option>
                    <a-select-option value="top-right">Top Right</a-select-option>
                    <a-select-option value="bottom-left">Bottom Left</a-select-option>
                    <a-select-option value="bottom-right">Bottom Right</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="Logo Size">
                  <a-select v-model:value="renderOptions.logoSize">
                    <a-select-option value="small">Small</a-select-option>
                    <a-select-option value="medium">Medium</a-select-option>
                    <a-select-option value="large">Large</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="Logo File">
              <a-upload
                v-model:file-list="logoFileList"
                :before-upload="beforeLogoUpload"
                accept="image/*"
                :max-count="1"
              >
                <a-button>
                  <upload-outlined />
                  Select Logo
                </a-button>
              </a-upload>
            </a-form-item>
          </div>
        </a-form-item>

        <!-- Audio Options -->
        <a-form-item label="Audio Settings" class="border border-gray-600 p-2 rounded-lg">
          <a-checkbox v-model:checked="renderOptions.addBackgroundMusic">
            Add background music
          </a-checkbox>
          <div v-if="renderOptions.addBackgroundMusic" class="mt-2">
            <a-form-item label="Background Music File">
              <a-upload
                v-model:file-list="musicFileList"
                :before-upload="beforeMusicUpload"
                accept="audio/*"
                :max-count="1"
              >
                <a-button>
                  <upload-outlined />
                  Select Music
                </a-button>
              </a-upload>
            </a-form-item>
            <a-form-item label="Background Music Volume">
              <a-slider
                v-model:value="renderOptions.backgroundMusicVolume"
                :min="0"
                :max="100"
                :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
              />
            </a-form-item>
          </div>

          <a-form-item label="Âm thanh video gốc" class="mt-2">
            <a-checkbox v-model:checked="renderOptions.holdMusicOnly">
              Chỉ giữ lại nhạc nền
            </a-checkbox>
            <a-checkbox v-model:checked="renderOptions.holdOriginalAudio">
              Giữ lại âm thanh video gốc
            </a-checkbox>
          </a-form-item>
          <!-- âm lượng âm thanh video gốc -->
          <a-form-item label="Âm lượng âm thanh video gốc" v-if="renderOptions.holdOriginalAudio">
            <a-slider
              v-model:value="renderOptions.originalAudioVolume"
              :min="0"
              :max="100"
              :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }"
            />
          </a-form-item>
        </a-form-item>

        <!-- Output Options -->
        <a-form-item label="Output Settings">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="Video Quality">
                <a-select v-model:value="renderOptions.videoQuality">
                  <a-select-option value="720p/16:9">720p HD</a-select-option>
                  <a-select-option value="1080p/16:9">Full HD</a-select-option>
                  <a-select-option value="4k/16:9">4K Ultra HD</a-select-option>
                  <a-select-option value="1080p/9:16">Full HD Dọc</a-select-option>
                  <a-select-option value="4k/9:16">4K Ultra HD Dọc</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="Frame Rate">
                <a-select v-model:value="renderOptions.frameRate">
                  <a-select-option value="24">24 fps</a-select-option>
                  <a-select-option value="30">30 fps</a-select-option>
                  <a-select-option value="60">60 fps</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
        </a-form>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, reactive, nextTick, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  UploadOutlined,
  // BlurOutlined,
  DeleteOutlined,
  FontColorsOutlined,
  ClearOutlined,
  UndoOutlined,
  DragOutlined
} from '@ant-design/icons-vue';
import { useTTSStore } from '@/stores/ttsStore';
import VideoPreviewDraw from './VideoPreviewDraw.vue';

const ttsStore = useTTSStore();
const isProcessing = ref(false);
const modalVisible = ref(false);
const logoFileList = ref([]);
const musicFileList = ref([]);

// Video preview refs
const videoPreviewDraw = ref(null);
const videoDuration = ref(0);
const currentTime = ref(0);
const blurAreas = ref([]);



// Time range for applying effects
const timeRange = reactive({
  start: 0,
  end: 0
});

// Render options configuration
const renderOptions = reactive({
  // Text options
  showText: true,
  fontSize: 24,
  textColor: '#fff700',
  textValue: '@Hello World',
  textOpacity: 50,
  textDirection: 'random',

  // Text Subtitle options
  showSubtitle: true,
  subtitleFontSize: 48,
  subtitleTextColor: '#ffffff',
  subtitleBackgroundColor: '#000000',
  subtitleBorderColor: '#000000',
  subtitleBold: true,
  shadowSize: 2,
  assOptions: {
    posX: -35,
    posY: 70,
    rotation: 0,
    align: 8,
    fontSize: 48,
  },

  // Logo options
  showLogo: false,
  logoPosition: 'bottom-right',
  logoSize: 'medium',

  // Audio options
  addBackgroundMusic: false,
  backgroundMusicVolume: 30,
  originalAudioVolume: 80,
  holdOriginalAudio: false,
  holdMusicOnly: false,

  // Output options
  videoQuality: '1080p/16:9',
  frameRate: '30'
});

const canRender = computed(() => {
  return ttsStore.currentSrtList && ttsStore.currentSrtList.items.length > 0;
});

// Subtitle color presets
const subtitleColorPresets = [
  // { name: 'None', textColor: 'transparent', backgroundColor: 'transparent', borderColor: 'transparent' },
  { name: 'White', textColor: '#FFFFFF', backgroundColor: '#000000', borderColor: '#000000' },
  { name: 'Black', textColor: '#000000', backgroundColor: '#FFFFFF', borderColor: '#FFFFFF' },
  { name: 'Blue', textColor: '#FFFFFF', backgroundColor: '#0066CC', borderColor: 'transparent' },
  { name: 'Purple', textColor: '#FFFFFF', backgroundColor: '#6600CC', borderColor: 'transparent' },
  { name: 'Yellow', textColor: '#000000', backgroundColor: '#FFCC00', borderColor: 'transparent' },
  { name: 'Blue Glow', textColor: '#00CCFF', backgroundColor: 'transparent', borderColor: '#0066CC' },
  { name: 'White Glow', textColor: '#FFFFFF', backgroundColor: 'transparent', borderColor: '#000000' },
  { name: 'Green', textColor: '#00FF00', backgroundColor: 'transparent', borderColor: '#006600' },
  { name: 'Pink', textColor: '#FF66CC', backgroundColor: 'transparent', borderColor: '#CC3399' },
  { name: 'Rainbow', textColor: '#FF6600', backgroundColor: 'transparent', borderColor: '#CC3300' },
  { name: 'Gold', textColor: '#FFD700', backgroundColor: 'transparent', borderColor: '#B8860B' }
];

// Convert CSS color to ASS color format
function cssToASSColor(cssColor, opacity = '00') {
  // Remove # if present
  const hex = cssColor.replace('#', '');

  if (hex === 'transparent' || hex === '') {
    return '&HFF000000';
  }

  // Convert hex to RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // ASS format: &H + opacity + BB + GG + RR (BGR format)
  const assColor = `&H${opacity}${b.toString(16).padStart(2, '0').toUpperCase()}${g.toString(16).padStart(2, '0').toUpperCase()}${r.toString(16).padStart(2, '0').toUpperCase()}`;

  return assColor;
}

// Apply color preset to subtitle options
function applyColorPreset(preset) {
  renderOptions.subtitleTextColor = preset.textColor;
  renderOptions.subtitleBackgroundColor = preset.backgroundColor;
  renderOptions.subtitleBorderColor = preset.borderColor;

  console.log('Applied color preset:', preset.name);
  console.log('ASS Colors:', {
    text: cssToASSColor(preset.textColor),
    background: cssToASSColor(preset.backgroundColor, '90'),
    border: cssToASSColor(preset.borderColor)
  });

  message.success(`Applied ${preset.name} color preset`);
}

// Get current ASS colors for debugging
function getCurrentASSColors() {
  return {
    text: cssToASSColor(renderOptions.subtitleTextColor),
    background: cssToASSColor(renderOptions.subtitleBackgroundColor, '90'),
    border: cssToASSColor(renderOptions.subtitleBorderColor)
  };
}


function showRenderModal() {
  if (!canRender.value) return;
  modalVisible.value = true;
}

function handleModalCancel() {
  modalVisible.value = false;
}

function beforeLogoUpload(file) {
  // Validate logo file
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('Please select a valid image file!');
    return false;
  }

  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('Logo file must be smaller than 5MB!');
    return false;
  }

  renderOptions.logoFile = file;
  return false; // Prevent auto upload
}

function beforeMusicUpload(file) {
  // Validate music file
  const isAudio = file.type.startsWith('audio/');
  if (!isAudio) {
    message.error('Please select a valid audio file!');
    return false;
  }

  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('Music file must be smaller than 50MB!');
    return false;
  }

  renderOptions.musicFile = file;
  return false; // Prevent auto upload
}

// Event handlers for VideoPreviewDraw component
function handleBlurAreasUpdated(data) {
  // Handle both old format (array) and new format (object with areas and assOptions)
  if (Array.isArray(data)) {
    blurAreas.value = data;
  } else {
    blurAreas.value = data.areas || [];
    // Store ASS options for video coordinates
    if (data.assOptions) {
      renderOptions.assOptionsForVideo = data.assOptions;
    }
  }
}

function handleTimeRangeUpdated(range) {
  timeRange.start = range.start;
  timeRange.end = range.end;
}

function onVideoLoaded(video) {
  videoDuration.value = video.duration;
  timeRange.end = video.duration;
}

function onVideoTimeUpdate(time) {
  currentTime.value = time;
}

async function handleRenderConfirm() {
  isProcessing.value = true;

  try {
    // Prepare render configuration
    const renderConfig = {
      srtItems: ttsStore.currentSrtList.items,
      srtPath: ttsStore.currentSrtList.path,
      blurAreas: blurAreas.value,
      timeRange: timeRange,
      options: {
        textAnimation: {
          enabled: renderOptions.showText,
          fontSize: renderOptions.fontSize,
          color: renderOptions.textColor,
          value: renderOptions.textValue,
          opacity: renderOptions.textOpacity / 100,
          directionType: renderOptions.textDirection
        },
        textSubtitle: {
          enabled: renderOptions.showSubtitle,
          fontSize: renderOptions.subtitleFontSize,
          color: renderOptions.subtitleTextColor,
          backgroundColor: renderOptions.subtitleBackgroundColor,
          borderColor: renderOptions.subtitleBorderColor,
          bold: renderOptions.subtitleBold,
          // ASS format colors for advanced subtitle rendering
          assColors: {
            text: cssToASSColor(renderOptions.subtitleTextColor),
            background: cssToASSColor(renderOptions.subtitleBackgroundColor),
            border: cssToASSColor(renderOptions.subtitleBorderColor),
            shadowSize: renderOptions.shadowSize
          },
          // ASS options with video coordinates
          assOptions: renderOptions.assOptionsForVideo || renderOptions.assOptions
        },
        logo: {
          enabled: renderOptions.showLogo,
          position: renderOptions.logoPosition,
          size: renderOptions.logoSize,
          file: renderOptions.logoFile
        },
        audio: {
          backgroundMusic: {
            enabled: renderOptions.addBackgroundMusic,
            file: renderOptions.musicFile,
            volume: renderOptions.backgroundMusicVolume / 100
          },
          originalVolume: renderOptions.originalAudioVolume / 100,
          holdOriginalAudio: renderOptions.holdOriginalAudio,
          holdMusicOnly: renderOptions.holdMusicOnly
        },
        output: {
          quality: renderOptions.videoQuality,
          frameRate: parseInt(renderOptions.frameRate)
        }
      }
    };
    console.log('renderConfig', renderConfig);
    // Call the electron API with enhanced options
    electronAPI.processVideoWithOptions(JSON.parse(JSON.stringify(renderConfig)));

    // Simulate processing time
    await new Promise((resolve) => setTimeout(resolve, 2000));

    message.success('Video rendered successfully with custom options and blur areas!');
    modalVisible.value = false;

  } catch (error) {
    message.error('Error rendering video: ' + error.message);
  } finally {
    isProcessing.value = false;
  }
}






</script>

<style scoped>
.mt-2 {
  margin-top: 8px;
}



/* Color Presets Styles */
.color-presets-section {
  margin-bottom: 16px;
}

.color-presets-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.color-preset-item {
  position: relative;
}

.color-preset-item.active .color-preview {
  box-shadow: 0 0 0 3px #1890ff;
  transform: scale(1.05);
}

.color-preview {
  transition: all 0.2s ease;
  user-select: none;
}

.color-preview:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.subtitle-preview {
  /* border: 1px solid #d9d9d9; */
  border-radius: 4px;
  padding: 8px;
  /* background: #f5f5f5; */
  display: flex;
  align-items: center;
  justify-content: center;
}

.subtitle-preview-content {
  margin: 0;
  max-width: 100%;
  word-wrap: break-word;
}
</style>